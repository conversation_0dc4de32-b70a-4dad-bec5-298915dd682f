node_modules
build
certs
cert
coverage
dist
.npmrc
.yarnrc.yml

# Environment variables
.env
.env.local
.env.*.local

# Yarn
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# TypeScript
.tsbuildinfo

# Remix
.cache/
.remix/

# Locales
locales_cache/

# IDE
.vscode/
.idea/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ESLint
.eslintcache

# Playwright
test-results/
playwright-report/

# DS_Store
.DS_Store

# Dev Log
dev.log

# Public Build
public/build/

# Wrangler
.wrangler/