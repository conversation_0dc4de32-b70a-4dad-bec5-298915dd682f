import "reflect-metadata/lite";
import "@testing-library/jest-dom";

import { cleanup } from "@testing-library/react";
import ResizeObserver from "resize-observer-polyfill";
import { afterEach, vi, beforeAll } from "vitest";

global.ResizeObserver = ResizeObserver;

beforeAll(async () => {
  // await iniateTestInstance();
});

// runs a cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup();
});

Object.defineProperty(window, "matchMedia", {
  writable: false,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

Object.defineProperty(window, "scrollTo", {
  writable: false,
  value: vi.fn(),
});

const IntersectionObserverMock = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  takeRecords: vi.fn(),
  unobserve: vi.fn(),
}));

vi.stubGlobal("IntersectionObserver", IntersectionObserverMock);

const ignoredMessages = ["Could not parse CSS stylesheet"];

const originalError = console.error;

vi.spyOn(console, "error").mockImplementation((...args) => {
  const [message] = args;
  if (typeof message === "string" && ignoredMessages.some((v) => message.includes(v))) {
    return;
  }

  originalError(...args);
});
