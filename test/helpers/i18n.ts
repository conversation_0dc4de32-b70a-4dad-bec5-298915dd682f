// eslint-disable-next-line @typescript-eslint/no-restricted-imports -- baseI18n instance for test
import { createInstance } from "i18next";
import FsBackend from "i18next-fs-backend";
import { initReactI18next } from "react-i18next";

import { i18nOptions } from "../../app/i18n/i18n-options";
export const baseI18Next = createInstance();
export const cacheFilePattern = "./locales_cache/test.{{lng}}.json";

export const iniateTestInstance = () =>
  baseI18Next
    .use(initReactI18next)
    .use(FsBackend)
    .init({
      backend: {
        loadPath: cacheFilePattern,
      },
      ...i18nOptions,
    });
