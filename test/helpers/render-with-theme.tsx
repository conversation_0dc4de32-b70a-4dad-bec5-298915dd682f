import createEmotionCache from "@emotion/cache";
import { render } from "@testing-library/react";
import { ReactElement } from "react";

import { CoreThemeProvider } from "~/context/coreProvider";

export const renderWithTheme = (ui: ReactElement) => {
  const cache = createEmotionCache({ key: "roshn-boilerplate-fe" });

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <CoreThemeProvider cache={cache}>{children}</CoreThemeProvider>
  );

  return render(ui, { wrapper: Wrapper });
};
