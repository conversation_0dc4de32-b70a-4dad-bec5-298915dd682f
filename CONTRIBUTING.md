# Contributing to ROSHN Seller Dashboard

First off, thank you for considering contributing to the ROSHN Seller Dashboard! It's people like you that make this
project great.

## Table of Contents

- [Getting Started](#getting-started)
- [Making Changes](#making-changes)
- [Submitting a Pull Request](#submitting-a-pull-request)
- [Code of Conduct](#code-of-conduct)
- [License](#license)

## Getting Started

### 1. Fork & Clone the Repository

First, you'll need to fork the repository to your own GitHub account. You can do this by clicking the "Fork" button on
the top right of the repository page.

Once you have forked the repository, you can clone it to your local machine:

```bash
git clone https://gitlab.com/ROSHN.com/roshn-com-application/frontend/roshn-seller-dashboard-ui.git
cd roshn-seller-dashboard-ui
```

### 2. Create `.npmrc` file

This project uses a private GitLab package registry. You'll need to create a `.npmrc` file in the root of the project to
authenticate.

Run the following command to create the `.npmrc` file. You will need to get a personal access token from GitLab with
`read_api` and `read_package_registry` scopes and replace the `${GITLAB_TOKEN}` placeholder.

```bash
cat << EOF > .npmrc
//gitlab.com/api/v4/projects/********/packages/npm/:_authToken=\${GITLAB_TOKEN}
always-auth=true
@roshn:registry=https://gitlab.com/api/v4/projects/********/packages/npm/
EOF
```

### 3. Install Dependencies

This project uses `pnpm` for package management. To install the dependencies, run the following command:

```bash
pnpm install
```

### 4. Run the Development Server

To start the development server, run:

```bash
pnpm run dev
```

This will start the application on `https://localhost:5173`.

## Making Changes

For details on the architectural patterns used in this project, please see the
[Architecture documentation](docs/ARCHITECTURE.md).

### 1. Create a New Branch

It's important to create a new branch for each feature or bug fix you're working on. Use a descriptive name for your
branch:

```bash
git checkout -b feature/JIRA-TICKET
```

### 2. Running Tests, Linting, and Formatting

Before you commit your changes, please make sure that the code is clean and that all tests pass.

- **Run Tests:**

  ```bash
  pnpm test
  ```

- **Run Linting:**

  ```bash
  pnpm run lint
  ```

- **Run Formatting:**
  ```bash
  pnpm run format
  ```

### 3. End-to-End Testing

For details on how to run and write end-to-end (E2E) tests, please see the [E2E Testing Guide](docs/E2E_TESTING.md).

You can also run `pnpm run validate` to run type checking, linting, and formatting checks all at once. To fix formatting
and linting issues automatically, you can use `pnpm run fix`.

## Submitting a Pull Request

### Commit Message Conventions

This project uses [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/) for commit messages. We use
`commitizen` to help you create a valid commit message. To commit your changes, use the following command:

```bash
pnpm run cm
```

This will guide you through creating a properly formatted commit message.

### Create a Pull Request

Once you have committed your changes, push your branch to your forked repository:

```bash
git push origin feature/JIRA-TICKET
```

Then, go to the original repository on GitHub and you will see a prompt to create a new pull request from your branch.
Please provide a clear and descriptive title and description for your pull request.

## Code of Conduct

This project and everyone participating in it is governed by the [Code of Conduct](CODE_OF_CONDUCT.md). By
participating, you are expected to uphold this code.

## License

By contributing to this project, you agree that your contributions will be licensed under the MIT License.
