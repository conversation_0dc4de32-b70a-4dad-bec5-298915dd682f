## 📝 Description

Please include a summary of the changes and the problem that is solved. Please also include relevant motivation and
context.

## ✔️ Checklist

- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] I have disclosed if any part of this code was generated with the help of an AI.

## 🤖 AI-Generated Code Disclosure

If you used an AI to generate any part of this code, please provide details below on which parts were generated and
which AI tool you used.

## 🖼️ Screenshots (if applicable)

| Before | After |
| ------ | ----- |
|        |       |

## 🎨 Figma Link

Please provide a link to the Figma design file if applicable.

[Link to Figma Design](https://www.figma.com/...)

## 📎 Additional context

Add any other context about the problem here.
