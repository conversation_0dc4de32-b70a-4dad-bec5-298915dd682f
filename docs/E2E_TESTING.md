# End-to-End (E2E) Testing Guide

This document outlines the structure and best practices for writing end-to-end tests in this project using Playwright.

## Running Tests

You can run the entire test suite using the following commands:

- **Run in headless mode:**

  ```bash
  pnpm e2e
  ```

- **Run in UI mode for debugging:**
  ```bash
  pnpm e2e:ui
  ```

## Folder Structure

The `playwright` directory contains all E2E testing-related code, organized as follows:

- **`/playwright/e2e`**: This is where the actual test files (`.spec.ts`) reside. Tests are grouped by feature into
  subdirectories.
  - **`/e2e/auth`**: Tests related to authentication (login, logout, etc.).
  - **`/e2e/smoke`**: Simple tests to verify that the application is running correctly.
  - **`/e2e/journeys`**: For long-form tests that cover a complete user workflow from start to finish.
- **`/playwright/pages`**: This directory contains Page Object Models (POMs). Each file corresponds to a page in the
  application and encapsulates the locators and actions for that page.
- **`/playwright/fixtures`**: Contains test data files (e.g., `users.json`) to decouple data from test logic.
- **`/playwright/support`**: This directory holds reusable utility code.
  - **`utils.ts`**: For general-purpose helper functions (e.g., `generateRandomEmail`).
  - **`commands.ts`**: For creating reusable custom actions that operate on the `page` object.

## Core Concepts & Best Practices

### Authentication

For tests that require a logged-in state, use a `beforeEach` hook within your `test.describe` block to perform the login
action. This ensures each test starts fresh but avoids repeating login code within every single test function.

### Page Object Model (POM)

- **Use Page Objects**: Always use the Page Object Model to separate page interaction logic from your tests. Your test
  should read like a user story, not a series of clicks and CSS selectors.
- **Good Selectors**: Prefer user-facing attributes like text content or ARIA roles (`getByRole`, `getByText`) over
  brittle selectors like CSS or XPath.
- **Don't Use `page.waitFor()`**: Playwright's auto-waiting mechanism is very powerful. Avoid using manual waits. Use
  web-first assertions (`expect(locator).toBeVisible()`) which will wait for the condition to be met.

### Managing Test Data

To keep tests clean, test data (like user credentials) is stored separately from test logic.

- **Location**: All test data is located in the `/playwright/fixtures` directory.
- **Format**: Data is stored in JSON files (e.g., `users.json`).
- **Usage**: You can import the JSON file directly into your test or setup files.

```typescript
import user from "../../fixtures/users.json" with { type: "json" };

test("some test", () => {
  const username = userData.standard_user.email;
  // ...
});
```

## Writing a New Test

1.  **Create a Page Object**: If you are testing a new page, first create a new Page Object in the `/playwright/pages`
    directory. This class should contain locators for the elements on the page and methods that perform actions (e.g.,
    `fillForm()`, `clickSubmit()`).

2.  **Create a Spec File**: Create a new `.spec.ts` file in the appropriate subdirectory under `/playwright/e2e`.

3.  **Write the Test**: Import your Page Object and use it to interact with the page. Your test should focus on
    asserting application behavior, not on the implementation details of the page.

### Example

```typescript
// in playwright/e2e/some-feature/feature.spec.ts
import { test, expect } from "@playwright/test";
import { SomePage } from "../../pages/SomePage";

test("should do something on SomePage", async ({ page }) => {
  const somePage = new SomePage(page);
  await somePage.goto();
  await somePage.performSomeAction("some data");
  await expect(somePage.someElement).toBeVisible();
});
```

## Best Practices

- **Use Page Objects**: Always use the Page Object Model to separate page interaction logic from your tests.
- **Good Selectors**: Prefer user-facing attributes like text content or ARIA roles (`getByRole`, `getByText`) over
  brittle selectors like CSS or XPath.
- **Don't Use `page.waitFor()`**: Playwright's auto-waiting mechanism is very powerful. Avoid using manual waits. Use
  web-first assertions (`expect(locator).toBeVisible()`) which will wait for the condition to be met.
- **Group Tests**: Use `test.describe()` to group related tests together in a spec file.
