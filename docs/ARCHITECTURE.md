# Architecture

This document provides an overview of the architectural patterns used in this project.

## Dependency Injection

We use [InversifyJS](https://inversify.io/) for Dependency Injection (DI) to manage dependencies between different parts
of the application. This helps in building a decoupled and testable codebase.

### Core Concepts

- **Container**: The Inversify container is created with `createContainer()` from `app/di/container.ts`. It's configured
  to use a `Singleton` scope by default, meaning each binding will resolve to the same instance throughout the
  application's lifecycle.
- **Bindings**: Services are "bound" to the container. We have separate binding files for different environments:
  - `app/di/bind-impls-common.ts`: for services that are used on both the server and the client.
  - `app/di/bind-impls.client.ts`: for client-specific services.
  - `app/di/bind-impls.server.ts`: for server-specific services.
- **Injection**: Dependencies are injected into components using the `useInjection` hook from `app/hooks/use-di.tsx`.

### How to add a new service

1.  **Define the service**: Create an interface for your service and a `Symbol` to be used as a service identifier
    (e.g., `app/services/my-service/my-service.ts`).
2.  **Implement the service**: Create a class that implements the service interface. Use the `@injectable()` decorator
    on the class and `@inject()` on constructor parameters for its dependencies (e.g.,
    `app/services/my-service/my-service-impl.ts`).
3.  **Bind the service**: Add your service to the appropriate binding file in `app/di/`.

## Services

Services contain the core business logic of the application. Each service lives in its own directory under
`app/services/`.

A typical service consists of:

- `my-service.ts`: Defines the service interface and the InversifyJS service identifier `Symbol`.
- `my-service-impl.ts`: The actual implementation of the service.
- `index.ts`: Exports the public parts of the service.

## Data Fetching with React Query

For data fetching, we use `@tanstack/react-query`. While the project is set up to use it, the convention is to co-locate
React Query hooks with the service they are related to.

This is the proposed pattern:

1.  In your service directory (e.g., `app/services/users/`), create a new file for your hooks (e.g., `users-hooks.ts`).
2.  Inside this file, create your React Query hooks. These hooks should use a service to fetch the data.
3.  Export the hooks from the service's main `index.ts` file.

Example of a hook file (`app/services/users/users-hooks.ts`):

```typescript
import { useQuery } from "@tanstack/react-query";
import { useInjection } from "~/hooks/use-di";
import { UserService } from "./users";

export function useUsers() {
  const userService = useInjection(UserService);
  return useQuery({
    queryKey: ["users"],
    queryFn: () => userService.getUsers(),
  });
}
```

## Pages and Routing

### Pages

Page components are located in the `app/pages` directory. They are standard React components that represent a view in
the application.

### Routing

We use `remix-flat-routes` for file-based routing. The files in the `app/routes` directory define the application's
routes. The file and directory names map to the URL structure.

For example, to create a new page at `/my-new-page`:

1.  Create the page component: `app/pages/my-new-page/my-new-page.tsx`.
2.  Create the route file: `app/routes/($lang)+/_public+/my-new-page.tsx`. This route file should import and export the
    page component. The `($lang)+`, `_public+` parts of the path are conventions for layout nesting.

Please refer to the `remix-flat-routes` documentation for more details on the available routing conventions.

## Form Handling with React Hook Form

For all form-related logic, including validation and state management, this project uses
[React Hook Form](https://react-hook-form.com/). It's a performant, flexible, and easy-to-use library for handling forms
in React.

### Basic Usage

Here is a quick example of how to set up a simple form:

```tsx
import { useForm, SubmitHandler } from "react-hook-form";

type Inputs = {
  example: string;
  exampleRequired: string;
};

export default function MyFormComponent() {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<Inputs>();

  const onSubmit: SubmitHandler<Inputs> = (data) => console.log(data);

  return (
    {/* "handleSubmit" will validate your inputs before invoking "onSubmit" */}
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* register your input into the hook by invoking the "register" function */}
      <input defaultValue="test" {...register("example")} />

      {/* include validation with required or other standard HTML validation rules */}
      <input {...register("exampleRequired", { required: true })} />
      {/* errors will return when field validation fails  */}
      {errors.exampleRequired && <span>This field is required</span>}

      <input type="submit" />
    </form>
  );
}
```

### Schema Validation with Zod

For more complex validation scenarios, we recommend using schema-based validation with `Zod`. This project has `zod` and
`@hookform/resolvers` pre-installed.

First, define your schema, then pass it to the `useForm` hook using the `zodResolver`.

**Step 1:** Install `@hookform/resolvers`.

```bash
pnpm add @hookform/resolvers
```

**Step 2:** Define a `zod` schema and use it in your form.

```tsx
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

type LoginSchema = z.infer<typeof loginSchema>;

export default function LoginForm() {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginSchema>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = (data: LoginSchema) => {
    console.log(data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input {...register("email")} />
      <p>{errors.email?.message}</p>

      <input type="password" {...register("password")} />
      <p>{errors.password?.message}</p>

      <input type="submit" />
    </form>
  );
}
```

For more in-depth information, please refer to the official
[React Hook Form documentation](https://react-hook-form.com/get-started#Quickstart).

## API Mocking with MSW

We use [Mock Service Worker (MSW)](https://mswjs.io/) to mock API endpoints. This allows for isolated frontend
development and testing without relying on a live backend.

### How it Works

MSW intercepts outgoing requests from the application and returns mocked responses. It runs in the browser for local
development and in Node.js for testing.

The configuration lives in the `app/mocks/` directory:

- `app/mocks/browser.ts`: Configures the MSW worker for the browser.
- `app/mocks/server.ts`: Configures the MSW worker for Node.js (used in tests).
- `app/mocks/handlers/`: This directory should contain all the request handlers, organized by domain. For example,
  user-related mocks would go in `app/mocks/handlers/user-handlers.ts`.

### Running with Mocks

To run the development server with API mocking enabled, use the following command:

```bash
pnpm run dev:msw
```

This sets the `VITE_ENABLE_MSW` environment variable, which instructs the application to start the mock server.

### Creating New Mocks

1.  Create a new handler file in the `app/mocks/handlers/` directory (e.g., `product-handlers.ts`).
2.  Define your request handlers using MSW's `http` object.
3.  Add the new handlers to the `handlers` array in `app/mocks/handlers/handlers.ts`.

For tests, handlers can also be defined directly within the test file. This is useful for handlers that are specific to
a single test suite or for overriding a default handler for a specific test case. Use `server.use()` to add a temporary
handler for a specific test.

## File Naming Conventions

To maintain consistency across the codebase, please adhere to the following file naming conventions:

- **Components**: `kebab-case.tsx` (e.g., `button.tsx`, `user-profile.tsx`).
- **Services, Hooks, and Utilities**: `kebab-case.ts` (e.g., `user-service.ts`, `use-auth.ts`).
- **Pages**: Page components should be `kebab-case.tsx` inside a directory of the same name (e.g.,
  `app/pages/user-profile/user-profile.tsx`).
- **Routes**: Route files follow the `remix-flat-routes` convention, which is typically `kebab-case` with special
  characters for layouts and parameters (e.g., `($lang)+/_public+/user-profile.tsx`).
- **Styles**: `kebab-case.styles.ts` if using a separate style file for a component.
- **Tests**: `*.test.ts(x)` or `*.spec.ts(x)` (e.g., `button.test.tsx`, `user-service.spec.ts`).
