FROM europe-west4-docker.pkg.dev/roshn-shared-utilities/docker/node:20-alpine AS builder

WORKDIR /usr/src/app

RUN apk upgrade --no-cache \
    && apk add --no-cache libc6-compat build-base

COPY . .

ARG BUILD_ENV

ENV BUILD_ENV=${BUILD_ENV}
ENV CI=true

RUN npm install -g pnpm && pnpm build --mode ${BUILD_ENV}

FROM europe-west4-docker.pkg.dev/roshn-shared-utilities/docker/node:20-alpine

WORKDIR /usr/src/app

ENV NODE_ENV=production

RUN apk upgrade --no-cache \
    && apk add --no-cache ca-certificates tini \
    && npm install -g pnpm \
    && rm -rf /var/cache/apk/* \
    && rm -rf /var/spool/cron \
    && rm -rf /etc/crontabs \
    && rm -rf /etc/periodic \
    && find / -xdev \( -path /var/run/secrets -o -path /var/run/secrets/kubernetes.io/serviceaccount \) -prune -o -type d -perm +0002 -exec chmod o-w {} + \
    && find / -xdev \( -path /var/run/secrets -o -path /var/run/secrets/kubernetes.io/serviceaccount \) -prune -o -type f -perm +0002 -exec chmod o-w {} + \
    && sed -i -r "/^(node|root|nobody)/!d" /etc/group \
    && sed -i -r "/^(node|root|nobody)/!d" /etc/passwd \
    && rm -rf /etc/init.d /lib/rc /etc/conf.d /etc/inittab /etc/runlevels /etc/rc.conf /etc/logrotate.d \
    && rm -rf /etc/sysctl* /etc/modprobe.d /etc/modules /etc/mdev.conf /etc/acpi \
    && rm -rf /etc/fstab \
    && find /bin /etc /lib /sbin /usr -xdev \( -path /var/run/secrets -o -path /var/run/secrets/kubernetes.io/serviceaccount \) -prune -o \( \
    -iname hexdump -o \
    -iname chgrp -o \
    -iname ln -o \
    -iname od -o \
    -iname strings -o \
    -iname su -o \
    -iname wget -o \
    -iname nc -o \
    -iname vi -o \
    -iname nslookup -o \
    -iname sudo \
    \) -delete \
    && find /bin /etc /lib /sbin /usr -xdev \( -path /var/run/secrets -o -path /var/run/secrets/kubernetes.io/serviceaccount \) -prune -o -type l -exec test ! -e {} \; -delete \
    && chown -R node:node /usr/src/app

COPY --chown=node:node --chmod=0755 --from=builder /usr/src/app/ ./

USER node

HEALTHCHECK NONE

EXPOSE 3000

ENV PORT=3000

# Solved PID 1 issue
ENTRYPOINT ["/sbin/tini", "--"]

CMD ["pnpm", "run", "start"]
