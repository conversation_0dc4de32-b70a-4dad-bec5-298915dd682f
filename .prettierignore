# Dependencies
node_modules/
pnpm-lock.yaml
package-lock.json
yarn.lock

# Build outputs
build/
dist/
.cache/
public/build/
.remix/

# Generated files
coverage/
*.tsbuildinfo
.nyc_output/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env*

# Git
.git/
.gitignore

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo

# Binary files
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.ico
*.woff
*.woff2
*.ttf
*.eot
*.pdf

# Minified files
*.min.js
*.min.css
*.bundle.js
*.bundle.css

# Package manager files
.pnpm-store/
.yarn/

# Test files
__snapshots__/

# Localization cache
locales_cache/

# Other
*.map
*.d.ts.map 