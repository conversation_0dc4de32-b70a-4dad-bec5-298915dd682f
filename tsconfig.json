{
  "include": ["**/*.ts", "**/*.tsx", "**/.server/**/*.ts", "**/.server/**/*.tsx", "**/.client/**/*.ts", "**/.client/**/*.tsx"],
  "compilerOptions": {
    // Library and environment
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "target": "ES2022",
    "typeRoots": ["./node_modules/@types", "./app/types"],
    "types": ["node"],

    // Module system
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "isolatedModules": true,

    // JSX
    "jsx": "react-jsx",
    "jsxImportSource": "@emotion/react",

    // Type checking (balanced strict mode)
    "strict": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,

    // Code quality (less strict to avoid breaking existing code)
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,

    // Module and file system
    "allowJs": false,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,

    // Path mapping
    "baseUrl": ".",
    "paths": {
      "~/*": ["./app/*"]
    },

    // Decorators (for dependency injection)
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "useDefineForClassFields": true,
    "strictPropertyInitialization": false,

    // Build
    "noEmit": true,
    "incremental": true,
    "tsBuildInfoFile": ".tsbuildinfo"
  },
  "exclude": ["node_modules", "build", "public/build", ".cache", "coverage"],
  "ts-node": {
    "esm": true
  }
}
