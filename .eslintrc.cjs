module.exports = {
  root: true,
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
  },
  env: {
    browser: true,
    es2022: true,
    node: true,
  },
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:import/recommended",
    "plugin:import/typescript",
    "prettier", // Must be last to override other formatting rules
  ],
  plugins: [
    "@typescript-eslint",
    "react",
    "react-hooks",
    "jsx-a11y",
    "import",
  ],
  settings: {
    react: {
      version: "detect",
    },
    "import/resolver": {
      typescript: {
        alwaysTryTypes: true,
        project: "./tsconfig.json",
      },
    },
  },
  rules: {
    // TypeScript specific rules
    "@typescript-eslint/no-unused-vars": [
      "error", 
      { 
        argsIgnorePattern: "^_",
        varsIgnorePattern: "^_",
        caughtErrorsIgnorePattern: "^_",
      }
    ],
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/consistent-type-imports": [
      "off",
      { prefer: "type-imports" }
    ],
    "@typescript-eslint/consistent-type-definitions": ["off", "interface"],
    
    // React specific rules
    "react/react-in-jsx-scope": "off", // Not needed in React 17+
    "react/prop-types": "off", // TypeScript handles this
    "react/jsx-uses-react": "off", // Not needed in React 17+
    "react/jsx-uses-vars": "error",
    "react/no-unknown-property": ["error", { ignore: ["css"] }], // Allow Emotion css prop
    "react/self-closing-comp": "error",
    "react/jsx-boolean-value": ["error", "never"],
    "react/jsx-curly-brace-presence": ["error", "never"],
    "react/jsx-fragments": ["error", "syntax"],
    "react/jsx-no-useless-fragment": "error",
    "react/jsx-pascal-case": "error",
    "react/no-array-index-key": "warn",
    "react/no-unstable-nested-components": "error",
    
    // React Hooks rules
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    
    // Import rules
    "import/order": [
      "error",
      {
        groups: [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index",
        ],
        "newlines-between": "always",
        alphabetize: {
          order: "asc",
          caseInsensitive: true,
        },
      },
    ],
    "import/no-unresolved": "error",
    "import/no-duplicates": "error",
    "import/no-default-export": "off", // Remix needs default exports
    "import/prefer-default-export": "off",
    "import/no-cycle": "off",
    "import/no-self-import": "error",
    
    // General rules
    "no-console": "warn",
    "no-debugger": "error",
    "no-unused-expressions": "error",
    "prefer-const": "error",
    "no-var": "error",
    "object-shorthand": "error",
    "prefer-arrow-callback": "error",
    "prefer-template": "error",
    "no-nested-ternary": "warn",
    "no-unneeded-ternary": "error",
    "no-else-return": "error",
    "no-param-reassign": "warn",
    "no-shadow": "off", // Disabled in favor of TS version
    "@typescript-eslint/no-shadow": "off",
    
    // Accessibility
    "jsx-a11y/anchor-is-valid": [
      "error",
      {
        components: ["Link"],
        specialLink: ["to"],
      },
    ],
  },
  ignorePatterns: [
    "node_modules/",
    "playwright-report/",
    "build/",
    "public/build/",
    ".cache/",
    "coverage/",
    "*.config.js",
    "*.config.ts",
    "*.config.mjs",
    "*.config.cjs",
    ".tsbuildinfo",
    ".eslintcache",
  ],
  overrides: [
    {
      files: ["**/*.test.{ts,tsx}", "**/*.spec.{ts,tsx}"],
      env: {
        jest: true,
      },
      rules: {
        "@typescript-eslint/no-explicit-any": "off",
        "import/no-default-export": "off",
      },
    },
    {
      files: ["app/routes/**/*.{ts,tsx}", "app/root.tsx"],
      rules: {
        "import/no-default-export": "off", // Remix route files need default exports
      },
    },
    {
      files: ["vite.config.ts", "vitest.config.ts"],
      rules: {
        "import/no-default-export": "off",
      },
    },
  ],
}; 