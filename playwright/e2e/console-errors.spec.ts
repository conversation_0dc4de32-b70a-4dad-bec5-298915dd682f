import { test, expect, type ConsoleMessage } from "@playwright/test";

test.describe("Console Errors", () => {
  const testPaths = ["/en/shop", "/en/shop/broker"];

  // Scalable error filter patterns - add new patterns here as needed
  const acceptableErrorPatterns = [
    "Unrecognized Content-Security-Policy directive 'prefetch-src'",
    // Add more patterns here as needed:
    // "Failed to load resource: the server responded with a status of 404",
    // "Warning: React does not recognize",
    // "Non-passive event listener",
  ];

  // Function to check if an error should be filtered out
  const shouldFilterError = (errorText: string): boolean => {
    return acceptableErrorPatterns.some((pattern) => errorText.includes(pattern));
  };

  testPaths.forEach((path) => {
    test(`should not have console errors on ${path}`, async ({ page }) => {
      const consoleErrors: ConsoleMessage[] = [];

      // Listen for console errors
      page.on("console", (msg) => {
        if (msg.type() === "error") {
          const text = msg.text();

          // Only add to errors if it's not in our acceptable patterns
          if (!shouldFilterError(text)) {
            consoleErrors.push(msg);
          }
        }
      });

      // Navigate to the page
      await page.goto(path);
      await page.waitForLoadState("networkidle");

      // Log any errors found for debugging
      if (consoleErrors.length > 0) {
        console.log(`Console errors found on ${path}:`);
        consoleErrors.forEach((msg) => console.log(`- ${msg.text()}`));
      }

      // Fail if any console errors are found
      expect(consoleErrors).toHaveLength(0);
    });
  });
});
