import { test, expect } from "@playwright/test";

import user from "../../fixtures/users.json" with { type: "json" };
import { CustomerDetailsPage } from "../../pages/customer-details-page";
import { DashboardPage } from "../../pages/dashboard-page";
import { LoginPage } from "../../pages/login-page";
import { Notifications } from "../../pages/notification-page";

// Read user data from the fixture file

test.describe.skip("Customer Unit Assignment Journey", () => {
  test.beforeEach(async ({ page }) => {
    // Log in before each test in this suite.
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    await loginPage.login(user.standard_user.email, user.standard_user.password);
    await expect(page).toHaveURL(/.*dashboard/);
  });

  test("should allow a user to find a customer and assign a new unit", async ({ page }) => {
    // We start the test logged in, but must navigate to the dashboard.
    await page.goto("/dashboard");

    // Instantiate page objects
    const dashboardPage = new DashboardPage(page);
    const customerDetailsPage = new CustomerDetailsPage(page);
    const notifications = new Notifications(page);

    // Get test data from fixture
    const customerName = user.customer_to_find.name;
    const customerId = user.customer_to_find.id;

    await test.step("1. Search for the customer", async () => {
      await dashboardPage.searchForCustomer(customerName);
      // After search, we expect to be on the customer details page
      await expect(page).toHaveURL(new RegExp(`.*customer/${customerId}`));
      await expect(page.getByText(customerName)).toBeVisible();
    });

    await test.step("2. Initiate and confirm unit assignment", async () => {
      await customerDetailsPage.startUnitAssignment();
      await customerDetailsPage.confirmUnitAssignment();
    });

    await test.step("3. Verify the success notification", async () => {
      await notifications.expectSuccessWithMessage("Unit assigned successfully!");
    });
  });
});
