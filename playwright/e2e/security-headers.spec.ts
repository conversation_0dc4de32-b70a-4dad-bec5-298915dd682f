import { test, expect, type Response } from "@playwright/test";

test.describe("Security Headers", () => {
  let response: Response | null;

  test.beforeEach(async ({ page }) => {
    // Navigate to the main page and capture the response
    response = await page.goto("/en/shop");
    expect(response).not.toBeNull();
  });

  test.describe("Content Security Policy (CSP)", () => {
    test("should have CSP header present", async () => {
      const cspHeader = response?.headers()["content-security-policy"];
      expect(cspHeader).toBeDefined();
      expect(cspHeader).not.toBe("");
    });

    test("should have secure default-src directive", async () => {
      const cspHeader = response?.headers()["content-security-policy"];
      expect(cspHeader).toContain("default-src");
      expect(cspHeader).toContain("'self'");
    });

    test("should have script-src with nonce and strict-dynamic", async () => {
      const cspHeader = response?.headers()["content-security-policy"];
      expect(cspHeader).toContain("script-src");
      expect(cspHeader).toContain("'self'");
      expect(cspHeader).toContain("'strict-dynamic'");
      // Should contain a nonce
      expect(cspHeader).toMatch(/script-src[^;]*'nonce-[^']+'/);
    });

    test("should have restrictive frame-ancestors", async () => {
      const cspHeader = response?.headers()["content-security-policy"];
      expect(cspHeader).toContain("frame-ancestors");
      expect(cspHeader).toContain("'none'");
    });

    test("should have restrictive object-src", async () => {
      const cspHeader = response?.headers()["content-security-policy"];
      expect(cspHeader).toContain("object-src");
      expect(cspHeader).toContain("'none'");
    });

    test("should have base-uri restriction", async () => {
      const cspHeader = response?.headers()["content-security-policy"];
      expect(cspHeader).toContain("base-uri");
      expect(cspHeader).toContain("'none'");
    });

    test("should allow data: and https: for images", async () => {
      const cspHeader = response?.headers()["content-security-policy"];
      expect(cspHeader).toContain("img-src");
      expect(cspHeader).toContain("'self'");
      expect(cspHeader).toContain("data:");
      expect(cspHeader).toContain("https:");
    });

    test("should have style-src with unsafe-inline", async () => {
      const cspHeader = response?.headers()["content-security-policy"];
      expect(cspHeader).toContain("style-src");
      expect(cspHeader).toContain("'self'");
      expect(cspHeader).toContain("'unsafe-inline'");
    });

    test("should have form-action restricted to self", async () => {
      const cspHeader = response?.headers()["content-security-policy"];
      expect(cspHeader).toContain("form-action");
      expect(cspHeader).toContain("'self'");
    });

    test("should have connect-src configured", async () => {
      const cspHeader = response?.headers()["content-security-policy"];
      expect(cspHeader).toContain("connect-src");
      expect(cspHeader).toContain("'self'");
    });

    test("should have upgrade-insecure-requests directive", async () => {
      const cspHeader = response?.headers()["content-security-policy"];
      expect(cspHeader).toContain("upgrade-insecure-requests");
    });
  });

  test.describe("HTTP Strict Transport Security (HSTS)", () => {
    test("should have HSTS header", async () => {
      const hstsHeader = response?.headers()["strict-transport-security"];
      expect(hstsHeader).toBeDefined();
      expect(hstsHeader).not.toBe("");
    });

    test("should have max-age directive", async () => {
      const hstsHeader = response?.headers()["strict-transport-security"];
      expect(hstsHeader).toMatch(/max-age=\d+/);
    });

    test("should include subdomains", async () => {
      const hstsHeader = response?.headers()["strict-transport-security"];
      expect(hstsHeader).toContain("includeSubDomains");
    });

    test("should have preload directive", async () => {
      const hstsHeader = response?.headers()["strict-transport-security"];
      expect(hstsHeader).toContain("preload");
    });
  });

  test.describe("Other Security Headers", () => {
    test("should have X-Content-Type-Options", async () => {
      const header = response?.headers()["x-content-type-options"];
      expect(header).toBe("nosniff");
    });

    test("should have X-Frame-Options", async () => {
      const header = response?.headers()["x-frame-options"];
      expect(header).toBe("DENY");
    });

    test("should have X-XSS-Protection", async () => {
      const header = response?.headers()["x-xss-protection"];
      expect(header).toBe("1; mode=block");
    });

    test("should have Referrer-Policy", async () => {
      const header = response?.headers()["referrer-policy"];
      expect(header).toBe("origin-when-cross-origin");
    });

    test("should have Cross-Origin-Resource-Policy", async () => {
      const header = response?.headers()["cross-origin-resource-policy"];
      expect(header).toBe("same-origin");
    });

    test("should have X-DNS-Prefetch-Control", async () => {
      const header = response?.headers()["x-dns-prefetch-control"];
      expect(header).toBe("on");
    });
  });

  test.describe("CSP Violation Prevention", () => {
    test("should block inline scripts without nonce", async ({ page }) => {
      // Navigate to a page
      await page.goto("/en/shop");

      // Try to inject an inline script without nonce
      const scriptExecuted = await page.evaluate(() => {
        try {
          const script = document.createElement("script");
          script.textContent = "(window as any).testScriptExecuted = true;";
          document.head.appendChild(script);
          return !!(window as any).testScriptExecuted;
        } catch (error) {
          return false;
        }
      });

      // The script should be blocked by CSP
      expect(scriptExecuted).toBe(false);
    });

    test("should allow scripts with valid nonce", async ({ page }) => {
      await page.goto("/en/shop");

      // Check that legitimate scripts with nonce are working
      // by verifying that the page loads correctly and React is working
      await expect(page.locator("body")).toBeVisible();

      // Verify that the page has loaded properly (indicating scripts with nonce work)
      // Check for common React indicators or just verify the page loaded without CSP errors
      const pageLoaded = await page.evaluate(() => {
        // Check if the page has loaded and doesn't have CSP violations
        return document.readyState === "complete" && document.body.children.length > 0;
      });
      expect(pageLoaded).toBe(true);
    });

    test("should block external scripts from unauthorized domains", async ({ page }) => {
      await page.goto("/en/shop");

      // Try to load a script from an unauthorized domain
      const scriptLoaded = await page.evaluate(() => {
        return new Promise((resolve) => {
          const script = document.createElement("script");
          script.src = "https://evil.example.com/malicious.js";
          script.onload = () => resolve(true);
          script.onerror = () => resolve(false);
          document.head.appendChild(script);

          // Timeout after 2 seconds
          setTimeout(() => resolve(false), 2000);
        });
      });

      // The script should be blocked by CSP
      expect(scriptLoaded).toBe(false);
    });

    test("should prevent framing by external sites", async ({ context }) => {
      // Create a new page that tries to frame our site
      const framingPage = await context.newPage();

      await framingPage.setContent(`
        <html>
          <body>
            <iframe id="target-frame" src="https://localhost:5173/en/shop"></iframe>
          </body>
        </html>
      `);

      // Wait a bit for the frame to load (or fail to load)
      await framingPage.waitForTimeout(2000);

      // Check if the frame loaded successfully
      const frameLoaded = await framingPage.evaluate(() => {
        const frame = document.getElementById("target-frame") as HTMLIFrameElement;
        try {
          // If we can access the frame's document, it loaded
          return frame.contentDocument !== null;
        } catch (error) {
          // If we get an error, the frame was blocked
          return false;
        }
      });

      // The frame should be blocked by X-Frame-Options: DENY
      expect(frameLoaded).toBe(false);
    });
  });

  test.describe("Development vs Production CSP", () => {
    test("should have appropriate connect-src for environment", async () => {
      const cspHeader = response?.headers()["content-security-policy"];
      expect(cspHeader).toContain("connect-src");

      // In development, should allow WebSocket connections
      if (process.env.NODE_ENV === "development") {
        expect(cspHeader).toContain("ws:");
      }

      // Should always allow self
      expect(cspHeader).toContain("'self'");
    });
  });

  test.describe("CSP Reporting", () => {
    test("should not have report-uri in production", async () => {
      const cspHeader = response?.headers()["content-security-policy"];

      // In production, we typically don't want report-uri for performance
      if (process.env.NODE_ENV === "production") {
        expect(cspHeader).not.toContain("report-uri");
      }
    });
  });
});

test.describe("Security Headers Integration", () => {
  test("should have all critical security headers present", async ({ page }) => {
    const response = await page.goto("/en/shop");
    expect(response).not.toBeNull();

    const headers = response!.headers();

    // Critical security headers that should always be present
    const criticalHeaders = [
      "content-security-policy",
      "strict-transport-security",
      "x-content-type-options",
      "x-frame-options",
      "referrer-policy",
    ];

    for (const headerName of criticalHeaders) {
      expect(headers[headerName]).toBeDefined();
      expect(headers[headerName]).not.toBe("");
    }
  });

  test("should maintain security headers across different routes", async ({ page }) => {
    // Only test routes that actually exist
    const routes = ["/en/shop", "/en/shop/broker"];

    for (const route of routes) {
      const response = await page.goto(route);
      if (response) {
        const cspHeader = response.headers()["content-security-policy"];
        const hstsHeader = response.headers()["strict-transport-security"];

        expect(cspHeader).toBeDefined();
        expect(hstsHeader).toBeDefined();
        expect(cspHeader).toContain("default-src");
        expect(hstsHeader).toContain("max-age");
      }
    }
  });
});
