import { test, expect } from "@playwright/test";

import { LoginPage } from "../../pages/login-page";

test.describe.skip("Authentication", () => {
  test("should allow a user to log in", async ({ page }) => {
    // Create a new instance of the LoginPage
    const loginPage = new LoginPage(page);

    // Navigate to the login page
    await loginPage.goto();

    // Perform the login action
    await loginPage.login("<EMAIL>", "password123");

    // Assert that the user is redirected to the dashboard
    await expect(page).toHaveURL(/.*dashboard/);
  });
});
