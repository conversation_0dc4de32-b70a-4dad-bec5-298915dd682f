import { test, expect } from "@playwright/test";

test.describe("Language Redirection from Root", () => {
  test.describe("when browser language is Arabic", () => {
    // Set the "accept-language" header for all tests in this describe block.
    test.use({
      locale: "ar-SA",
    });

    test("should redirect to the Arabic homepage", async ({ page }) => {
      // Go to the root of the site.
      await page.goto("/shop");
      await expect(page).toHaveURL("/ar/shop");
    });
  });

  test.describe("when browser language is English", () => {
    test.use({
      locale: "en-US",
    });

    test("should redirect to the English homepage", async ({ page }) => {
      await page.goto("/shop");
      await expect(page).toHaveURL("/en/shop");
    });
  });

  test.describe("when browser language is not supported", () => {
    test.use({
      locale: "fr-FR",
    });

    test("should redirect to the default (English) homepage", async ({ page }) => {
      await page.goto("/shop");
      await expect(page).toHaveURL("/en/shop");
    });
  });

  test.describe("when visiting a path with a trailing slash", () => {
    test("should remove the trailing slash and redirect", async ({ page }) => {
      // Go to a path that includes a trailing slash.
      await page.goto("/ar/shop/");

      // Assert that the final URL does not have the trailing slash.
      // We check this by waiting for the URL to be exactly '/ar'.
      await expect(page).toHaveURL("/ar/shop");
    });
  });
});
