import { type Page, type Locator, expect } from "@playwright/test";

/**
 * Represents the customer details page.
 */
export class CustomerDetailsPage {
  readonly page: Page;
  readonly assignUnitButton: Locator;
  readonly unitAssignmentModal: Locator;
  readonly confirmAssignmentButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.assignUnitButton = page.getByRole("button", { name: "Assign Unit" });
    this.unitAssignmentModal = page.locator("#unit-assignment-modal");
    this.confirmAssignmentButton = this.unitAssignmentModal.getByRole("button", {
      name: "Confirm",
    });
  }

  async startUnitAssignment() {
    await this.assignUnitButton.click();
    await expect(this.unitAssignmentModal).toBeVisible();
  }

  async confirmUnitAssignment() {
    await this.confirmAssignmentButton.click();
  }
}
