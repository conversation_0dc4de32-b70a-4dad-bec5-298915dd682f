import { type Page, type Locator, expect } from "@playwright/test";

/**
 * Represents a global notifications component.
 */
export class Notifications {
  readonly page: Page;
  readonly successToast: Locator;

  constructor(page: Page) {
    this.page = page;
    this.successToast = page.locator(".toast-success");
  }

  /**
   * Asserts that a success notification with the given text is visible.
   * @param message The expected text of the notification.
   */
  async expectSuccessWithMessage(message: string) {
    await expect(this.successToast).toBeVisible();
    await expect(this.successToast).toHaveText(message);
  }
}
