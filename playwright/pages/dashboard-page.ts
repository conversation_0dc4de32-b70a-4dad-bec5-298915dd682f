import { type Page, type Locator } from "@playwright/test";

/**
 * Represents the main dashboard page, visible after login.
 */
export class DashboardPage {
  readonly page: Page;
  readonly customerSearchInput: Locator;
  readonly searchButton: Locator;

  constructor(page: Page) {
    this.page = page;
    // Note: These selectors are examples. You should update them
    // to match your application's actual implementation.
    this.customerSearchInput = page.getByPlaceholder("Search for a customer...");
    this.searchButton = page.getByRole("button", { name: "Search" });
  }

  async searchForCustomer(customerName: string) {
    await this.customerSearchInput.fill(customerName);
    await this.searchButton.click();
  }
}
