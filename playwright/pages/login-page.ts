import { type Page, type Locator } from "@playwright/test";

/**
 * Represents the Login page.
 * Provides methods to interact with elements on the page.
 */
export class LoginPage {
  // Using readonly is a good practice for locators
  readonly page: Page;
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly loginButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.emailInput = page.getByLabel("Email");
    this.passwordInput = page.getByLabel("Password");
    this.loginButton = page.getByRole("button", { name: "Login" });
  }

  /**
   * Navigates to the login page.
   */
  async goto() {
    await this.page.goto("/login");
  }

  /**
   * Fills the login form and submits it.
   * @param email The user's email.
   * @param password The user's password.
   */
  async login(email: string, password: string) {
    await this.emailInput.fill(email);
    await this.passwordInput.fill(password);
    await this.loginButton.click();
  }
}
