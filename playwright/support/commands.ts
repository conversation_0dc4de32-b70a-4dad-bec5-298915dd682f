import { Page } from "@playwright/test";

/**
 * A simple helper function to accept a browser dialog.
 * This demonstrates how you can create reusable actions.
 * @param page The Playwright Page object.
 * @param expectedMessage Optional message to assert on the dialog.
 */
export async function acceptDialog(page: Page, expectedMessage?: string) {
  page.on("dialog", async (dialog) => {
    if (expectedMessage) {
      if (dialog.message() !== expectedMessage) {
        throw new Error(
          `Dialog message was not correct. Expected: "${expectedMessage}", Got: "${dialog.message()}"`,
        );
      }
    }
    await dialog.accept();
  });
}
