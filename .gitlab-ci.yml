include:
  - project: 'ROSHN.com/roshn-com-application/devops/cicd-pipelines'
    ref: main
    file: 'pipeline-nodejs-gke.yml'

stages:
  - install
  - validate
  - test
  - secret-detection
  - sast
  - sca
  - build
  - container-scan
  - mdr-log-status
  - waf-status
  - analyze
  - deploy
  - vapt
  - release-notes
  - release
  - notify
  - pages
  - publish

#application specific variables
variables:
  APP_NAME: 'seller-dashboard-ui'
  K8S_NAMESPACE: 'frontend-seller-dashboard-ui'
  APP_TYPE: fe
  CONTAINER_PORT: 3000
  NODE_IMAGE: 'node:20-alpine'
  LINT_ENABLED: 'true'
  UNIT_TEST_ENABLED: 'false'
  UNIT_TEST_COMMAND: ''
  UNIT_TEST_RESULT: ''
  CONFIGMAP_ENABLED: 'false'
  SECRET_ENABLED: 'false'
  GCP_SECRET_NAME: ''
  PACKAGE_MANAGER: 'pnpm'
  CUSTOM_BEFORE_SCRIPT: |
    echo "@roshn:registry=https://${CI_SERVER_HOST}/api/v4/projects/********/packages/npm/" > .npmrc
    echo "//${CI_SERVER_HOST}/api/v4/projects/********/packages/npm/:_authToken=${CI_JOB_TOKEN}" >> .npmrc
    echo "//${CI_SERVER_HOST}/api/v4/projects/********/packages/npm/:always-auth=true" >> .npmrc

.dev-variables:
  variables:
    CUSTOM_RUNNER: dev
    GCP_ACCOUNT_ID: 'roshn-com-dev'
    BUILD_ARGS: '
      --build-arg BUILD_ENV=dev
      '
    CONFIGMAP_DATA: ''

.uat-variables:
  variables:
    CUSTOM_RUNNER: uat
    GCP_ACCOUNT_ID: 'roshn-com-uat'
    BUILD_ARGS: '
      --build-arg BUILD_ENV=uat
      '
    CONFIGMAP_DATA: ''

.prod-variables:
  variables:
    CUSTOM_RUNNER: prod
    GCP_ACCOUNT_ID: 'roshn-com-prod'
    BUILD_ARGS: '
      --build-arg BUILD_ENV=production
      '
    CONFIGMAP_DATA: ''
#end application specific variables
