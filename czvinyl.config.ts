import type { CzVinylConfig } from "cz-vinyl";

const czvinylConfig: CzVinylConfig = {
  commitTypes: [
    {
      description: "Breaking change",
      emoji: "💥",
      value: "breaking",
    },
    {
      description: "Build process or auxiliary tool changes",
      emoji: "🤖",
      value: "chore",
    },
    {
      description: "CI related changes",
      emoji: "🎡",
      value: "ci",
    },
    {
      description: "Documentation only changes",
      emoji: "📘",
      value: "docs",
    },
    {
      description: "A new feature",
      emoji: "🚀",
      value: "feature",
    },
    {
      description: "A bug fix",
      emoji: "🐛",
      value: "fix",
    },
    {
      description: "A code change that improves performance",
      emoji: "⚡",
      value: "perf",
    },
    {
      description: "A code change that neither fixes a bug or adds a feature",
      emoji: "💡",
      value: "refactor",
    },
    {
      description: "Create a release commit",
      emoji: "🔖",
      value: "release",
    },
    {
      description: "Markup, white-space, formatting, missing semi-colons...",
      emoji: "🎨",
      value: "style",
    },
    {
      description: "Adding missing tests",
      emoji: "🧪",
      value: "test",
    },
  ],
  headerFormat: "[{ticket_id}]{type}: {emoji}  {subject}",
  skipTicketId: false,
  skipScope: true,
};

export default czvinylConfig;
