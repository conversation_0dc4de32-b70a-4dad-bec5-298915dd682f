export class BaseError extends Error {
  public readonly traceId: string;
  public readonly timestamp: string;
  public readonly code: string;
  public readonly status: number;
  public readonly isOperational: boolean;

  constructor({
    message,
    code,
    status,
    traceId,
    isOperational = true,
  }: {
    message: string;
    code: string;
    status: number;
    traceId?: string;
    isOperational?: boolean;
  }) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.status = status;
    this.traceId = traceId || this.generateTraceId();
    this.timestamp = new Date().toISOString();
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }

  private generateTraceId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  public toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      status: this.status,
      traceId: this.traceId,
      timestamp: this.timestamp,
      isOperational: this.isOperational,
      stack: this.stack,
    };
  }
}
