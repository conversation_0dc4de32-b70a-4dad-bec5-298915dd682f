import { http, HttpResponse } from "msw";

export const exmapleHandlers = [
  http.get("https://api.example.com/user", () => {
    return HttpResponse.json({
      id: "abc-123",
      firstName: "<PERSON>",
      lastName: "Maverick",
    });
  }),

  http.get(
    "https://dev-api.shop.myroshn.com/shopboxo/api/v1.2/ecommerce/products/?limit=20&offset=0&search=&search=&ordering=-marketplace_product__date_submitted&marketplace_product_status=",
    () => {
      return HttpResponse.json({
        count: 1,
        next: null,
        previous: null,
        results: [
          {
            buffer_time_after: "00:10:00",
            buffer_time_before: "00:05:00",
            categories: ["Electronics", "Gadgets"],
            combinations: [],
            created_date: "2025-06-30T12:00:00Z",
            cross_sell_groups: [],
            custom_attributes: [],
            description: "A high-end smart speaker with voice assistant support.",
            duration_range: { min: 15, max: 60 },
            durations: [],
            id: 1,
            images: [
              "https://via.placeholder.com/300x300.png?text=Smart+Speaker",
              "https://via.placeholder.com/300x300.png?text=Speaker+Side+View",
            ],
            interval: "daily",
            is_bookable: true,
            is_large_size: false,
            is_popular: true,
            manage_stock: true,
            marketplace_categories: [
              {
                id: 101,
                title: "Smart Devices",
              },
            ],
            marketplace_product: {
              status: "active",
              status_display: "Available",
              code: 200,
              note: "Live on marketplace",
            },
            max_future_booking: 30,
            minimum_notice: 2,
            minimum_notice_unit: "hours",
            minimum_notice_unit_display: "2 Hours",
            modifier_groups: [],
            operating_hours: [],
            per_item_quantity: 1,
            per_item_type: {
              value: "unit",
              label: "Unit",
            },
            price: "149.99",
            price_range: {
              min: "129.99",
              max: "169.99",
            },
            sale_price: "139.99",
            sale_price_range: {
              min: "119.99",
              max: "149.99",
            },
            share_link: "https://shop.example.com/product/smart-speaker",
            sku: "SMART-SPK-001",
            slug: "smart-speaker",
            status: "active",
            stock_quantity: 45,
            title: "Smart Speaker Pro",
            updated_date: "2025-06-30T14:00:00Z",
            variants: [],
            weight: 1.5,
            weight_unit: {
              value: "kg",
              label: "Kilogram",
            },
            youtube_link: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
          },
        ],
      });
    },
  ),

  http.get(
    "https://************.nip.io/cms/api/translations?filters[namespace][$eqi]=home-acquisition&locale=ar",
    () => {
      return HttpResponse.json({
        app: {
          name: "Roshn Seller Dashboard",
          description: "Manage your seller account and products",
          title: "A home designed for you",
        },
        navigation: {
          home: "Home",
          products: "Products",
          orders: "Orders",
          settings: "Settings",
          profile: "Profile",
          logout: "Logout",
        },
        actions: {
          save: "Save",
          cancel: "Cancel",
          delete: "Delete",
          edit: "Edit",
          create: "Create",
          search: "Search",
          filter: "Filter",
          clear: "Clear",
          apply: "Apply",
        },
        messages: {
          loading: "Loading...",
          error: "An error occurred",
          success: "Operation successful",
          confirmDelete: "Are you sure you want to delete this item?",
        },
      });
    },
  ),

  http.get("https://dev-api.shop.myroshn.com/shopboxo/api/v1/merchant/data", () => {
    return HttpResponse.json({
      id: 72,
      store_name: "Retal",
      slug: "real-estate-retal",
      slug_update_counter: 0,
      address: "Saudi Arabia, Ryadh",
      contact_email: "<EMAIL>",
      is_email_verified: true,
      phone: "8003030888",
      whatsapp_phone: "",
      country: "SA",
      description:
        "When Retal Urban Development joined the stellar Al Fozan Group of Companies and began to develop residential, commercial and mixed-use properties.\r\nRetal represents the quintessence of craftsmanship by embracing the virtues of urbanism. The last ten years have seen us reimagine properties using a holistic approach underpinning a unified visionary design principle and creating meaningful destinations with urban real estate solutions. Today, our total asset value accounts for SAR 7+ billion, with 7000+ units developed or under construction.",
      icon: "https://storage.googleapis.com/dev-roshn-backend-shopboxo-static-files/media/shopify/icons/RETAL-LOGO-WHITE-new_RRYMtJ3.png",
      store_type: "shopboxo",
      payment_type: "marketplace_pay",
      payment_types: ["marketplace_pay"],
      email: "<EMAIL>",
      sections: [142],
      payments: {},
      is_min_spend_active: false,
      min_spending: "0.00",
      max_spending: "0.00",
      inventory_tracking_enabled: false,
      navbar_icon:
        "https://storage.googleapis.com/dev-roshn-backend-shopboxo-static-files/media/merchant_data/navbar_icons/RETAL-LOGO-WHITE-new_HvRZQQO.png",
      navbar_icons: {
        "16": null,
        "32": null,
        "180": null,
      },
      navbar_logo:
        "https://storage.googleapis.com/dev-roshn-backend-shopboxo-static-files/media/shopify/navbar_logos/navbarlogo_Do9og3i.png",
      navbar_logo_position: "center",
      navbar_size: "medium",
      navbar_position: "fixed",
      language: "en",
      time_zone: "UTC",
      currency: 27,
      custom_domains: [],
      currency_code: "SAR",
      show_navbar_logo: true,
      google_analytics_code: null,
      google_analytics_code_alt: null,
      google_tag_manager_container_id: null,
      meta_pixel_id: null,
      palette: {
        id: 19,
        name: "Retal Theme",
        brand_color: "#865232",
        top_bar_color: "#c79474",
        top_bar_text_color: "#FFFFFF",
        background_color: "#865232",
        image: null,
        order: 0,
      },
      global_styles: null,
      product_page_settings: null,
      seller_category: 11,
      seller_category_data: {
        id: 11,
        title: "Developer",
        emoji: "developer",
        icon: null,
      },
      share_text: "",
      privacy_policy: "",
      refund_policy: "",
      shipping_policy: "",
      terms_of_service: "",
      customer_support_number: "8003030888",
      customer_support_email: "<EMAIL>",
      shopping_cart_note: null,
      social_links: null,
      store_url: "https://dev-web.shop.myroshn.com/real-estate-retal",
      store_preview_url: "https://dev-web.shop.myroshn.com/real-estate-retal",
      follow_business_hours: false,
      is_store_open: true,
      closed_until: null,
      is_onboarded: false,
      is_test_merchant: false,
      font_styles: null,
      theme: null,
      is_palette_custom: true,
      is_auto_archive_order_enabled: false,
      is_fnb: false,
      is_bookable: false,
      ecom_plan: [],
      auto_catalog_usage: "NOT_USED",
      is_view_only_link_active: true,
      marketplace: {
        slug: "brokerage",
        title: "ROSHN Brokerage",
        product_reviews_enabled: false,
        admin_product_listing_review_required: false,
        languages: [
          {
            code: "en",
            name: "English",
            name_local: "English",
            name_translated: "English",
            bidi: false,
          },
          {
            code: "ar",
            name: "Arabic",
            name_local: "العربيّة",
            name_translated: "Arabic",
            bidi: true,
          },
        ],
      },
    });
  }),
];
