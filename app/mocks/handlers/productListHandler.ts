import { http, HttpResponse } from "msw";

export const productListHandler = [
  http.get(
    "https://dev-api.shop.myroshn.com/shopboxo/api/v1.2/ecommerce/products/?limit=20&offset=0&search=&search=&ordering=-marketplace_product__date_submitted&marketplace_product_status=",
    () => {
      return HttpResponse.json({
        count: 63,
        next: "https://dev-api.shop.myroshn.com/shopboxo/api/v1.2/ecommerce/products/?limit=20&marketplace_product_status=&offset=20&search=&search=",
        previous: null,
        results: [
          // ... full product objects from the original file ...
        ],
      });
    },
  ),
];
