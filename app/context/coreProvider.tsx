import { Cache<PERSON>rovider, EmotionCache, ThemeProvider as EmotionThemeProvider } from "@emotion/react";
import { getThemeBare } from "@roshn/ui-kit";
import * as R from "ramda";
import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

import { FontFaces } from "~/theme/globalFonts";

type ThemeProviderProps = React.PropsWithChildren<{
  cache: EmotionCache;
  darkMode?: boolean;
  rtlMode?: boolean;
}>;

type Device = "mobile" | "tablet" | "desktop";

type ThemeContextValue = {
  applyUserTheme: (userTheme: any) => void;
};

const ThemeUpdateContext = createContext<ThemeContextValue | null>(null);

export const useThemeManager = () => {
  const ctx = useContext(ThemeUpdateContext);
  if (!ctx) {
    throw new Error("useThemeManager must be used within CoreThemeProvider");
  }
  return ctx;
};

export const CoreThemeProvider = ({ cache, darkMode, children }: ThemeProviderProps) => {
  const { i18n } = useTranslation();
  const locale = i18n.resolvedLanguage || "en";
  const direction = locale === "ar" ? "rtl" : "ltr";
  const [mounted, setMounted] = useState(false);
  const [device, setDevice] = useState<Device>("desktop");
  const [userTheme, setUserTheme] = useState<object | null>(null);

  const detectDevice = () => {
    if (typeof window === "undefined") return "desktop";
    const width = window.innerWidth;
    if (width < 768) {
      return "mobile";
    } else if (width >= 768 && width < 1024) {
      return "tablet";
    }
    return "desktop";
  };

  useEffect(() => {
    setMounted(true);
    setDevice(detectDevice());

    const handleResize = () => setDevice(detectDevice());
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const baseTheme = useMemo(
    () =>
      getThemeBare({
        direction,
        name: darkMode ? "dark" : "light",
        locale,
        device: mounted ? device : "desktop",
      }),
    [darkMode, locale, direction, device, mounted],
  );

  const mergedTheme = useMemo(() => {
    return userTheme ? R.mergeDeepRight(baseTheme, userTheme) : baseTheme;
  }, [baseTheme, userTheme]);

  useEffect(() => {
    if (mounted) {
      document.documentElement.setAttribute("dir", direction);
      document.documentElement.setAttribute("lang", locale);
    }
  }, [locale, direction, mounted]);

  const applyUserTheme = useCallback((userTheme: object) => {
    setUserTheme(userTheme);
  }, []);

  return (
    <ThemeUpdateContext.Provider value={{ applyUserTheme }}>
      <CacheProvider value={cache}>
        <EmotionThemeProvider theme={mergedTheme}>
          <FontFaces />
          {children}
        </EmotionThemeProvider>
      </CacheProvider>
    </ThemeUpdateContext.Provider>
  );
};
