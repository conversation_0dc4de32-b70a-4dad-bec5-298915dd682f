import type {
  StoreApi,
  StoreMutatorIdentifier,
  StateCreator,
  UseBoundStore,
  Mutate,
} from "zustand";
import { PersistOptions as ZPersistOptions, PersistStorage } from "zustand/middleware";
import { StoreMutators } from "zustand/vanilla";

export type Batcher = (callback: () => void) => void;

export type Store<T, Mo<PERSON> extends [StoreMutatorIdentifier, unknown][] = []> = UseBoundStore<
  Mutate<StoreApi<T>, Mos>
>;

export type PersistedStore<S> = Store<S> & StoreMutators<Store<S>, object>["zustand/persist"];

export type PersistOptions<S, PersistedState = S> = ZPersistOptions<S, PersistedState>;

export type CreateStoreOptions = {
  middlewares?: any[];
};

export type CreatePersistedStoreOptions<S, PersistedState = S> = {
  middlewares?: any[];
  persist: PersistOptions<S, PersistedState>;
};

export interface CreateStore {
  <S = object>(initializer: StateCreator<S>, options?: CreateStoreOptions): Store<S>;
  <S = object, PersistedState = S>(
    initializer: StateCreator<S>,
    options: CreatePersistedStoreOptions<S, PersistedState>,
  ): PersistedStore<S>;
}

export interface StoreFactory {
  createStore: CreateStore;

  setDefaultBatcher: (batcher: Batcher) => this;

  /**
   * Set default storage for all stores.
   *
   * To use localStorage, use `createJSONStorage(() => localStorage)`
   */
  setDefaultStorage: (storage: PersistStorage<any>) => this;
}

export const StoreFactory = Symbol("StoreFactory");
