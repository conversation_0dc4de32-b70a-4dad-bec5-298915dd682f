import { injectable } from "inversify";
import { getCookie, setC<PERSON>ie, removeC<PERSON>ie } from "typescript-cookie";
import { StateCreator, create } from "zustand";
import { persist, PersistStorage } from "zustand/middleware";

import { Batcher, CreatePersistedStoreOptions, StoreFactory } from "./store-factory";

@injectable()
export class StoreFactoryImpl implements StoreFactory {
  private static defaultBatcher: Batcher = (callback) => callback();

  private batcher: Batcher = StoreFactoryImpl.defaultBatcher;
  setDefaultBatcher = (batcher: Batcher) => {
    this.batcher = batcher;

    return this as this;
  };

  private static dumpStorage: PersistStorage<unknown> = {
    getItem: (name: string) => {
      const value = getCookie(name);
      return value ? JSON.parse(value) : null;
    },
    removeItem: (name: string) => {
      removeCookie(name);
    },
    setItem: (name: string, value: any) => {
      setCookie(name, JSON.stringify(value), { expires: 1 });
    },
  };

  private defaultStorage: PersistStorage<unknown> = StoreFactoryImpl.dumpStorage;

  setDefaultStorage = (storage: PersistStorage<unknown>) => {
    this.defaultStorage = storage;

    return this;
  };

  createStore: StoreFactory["createStore"] = (initializer: StateCreator<object>, options = {}) => {
    const { middlewares: argMiddlewares, persist: argPersist } =
      options as CreatePersistedStoreOptions<unknown>;

    let middlewares: any[] = [create];

    if (argPersist) {
      const storage: any = argPersist.storage ?? this.defaultStorage;

      if (!storage) throw new Error("Storage is required");

      middlewares.push((store: any) =>
        persist(store, {
          ...argPersist,
          storage,
        }),
      );
    }

    if (argMiddlewares) {
      middlewares = middlewares.concat(argMiddlewares);
    }

    middlewares = middlewares.reverse();
    let store: any = initializer;
    for (const middleware of middlewares) {
      store = middleware(store);
    }

    store.originSetState = store.setState;

    store.setState = (...args: any[]) => {
      this.batcher(() => store.originSetState(...args));
    };

    return store;
  };
}
