import { DEFAULT_LANGUAGE, SUPPORTED_LANGUAGES } from "~/i18n/i18n-options";
import { i18n } from "~/i18n/i18n.client";

import { LocaleService } from "./locale";

export const localeServiceClientImpl: LocaleService = {
  getLang: () => {
    const lang = i18n.resolvedLanguage;
    if (!lang) return DEFAULT_LANGUAGE;
    if ((SUPPORTED_LANGUAGES as readonly string[]).includes(lang)) return lang;
    return DEFAULT_LANGUAGE;
  },

  getLocale: () => {
    const lang = i18n.resolvedLanguage;
    if (!lang) return DEFAULT_LANGUAGE;
    let locale = lang?.startsWith("en") ? "en-GB" : lang;
    locale = locale?.toLowerCase();
    return locale;
  },
};
