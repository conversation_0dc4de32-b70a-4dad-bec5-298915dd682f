import { DEFAULT_LANGUAGE, SUPPORTED_LANGUAGES } from "~/i18n/i18n-options";

import { LocaleService } from "./locale";

export const createLocaleService = (lang: string): LocaleService => {
  return {
    getLang: () => {
      if (!lang) return DEFAULT_LANGUAGE;
      if ((SUPPORTED_LANGUAGES as readonly string[]).includes(lang)) return lang;
      return DEFAULT_LANGUAGE;
    },

    getLocale: () => {
      if (!lang) return DEFAULT_LANGUAGE;
      let locale = lang?.startsWith("en") ? "en-GB" : lang;
      locale = locale?.toLowerCase();
      return locale;
    },
  };
};
