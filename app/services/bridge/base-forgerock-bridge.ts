import { BaseForgeRockBridgeWeb } from "@roshn/forgerock-bridge/forgerock-bridge-impl";
import { injectable, inject } from "inversify";

import { EnvService } from "../env";

export const FRBridgeWeb = Symbol("ForgeRockBridgeWeb");

@injectable()
export class ForgeRockBridgeWeb extends BaseForgeRockBridgeWeb {
  constructor(@inject(EnvService) readonly envService: EnvService) {
    super({
      amUrl: envService.FR_R_AM_URL,
      oauthClient: envService.FR_R_OAUTH_CLIENT,
      oauthScope: envService.FR_R_OAUTH_SCOPE,
      realmPath: envService.FR_R_REALM_PATH,
      timeout: envService.FR_TIMEOUT,
    });
  }
}
