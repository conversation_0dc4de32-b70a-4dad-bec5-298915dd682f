import { AnalyticsEvent } from ".";

type WindowWithDataLayer = Window & {
  dataLayer: Record<string, any>[];
};

declare const window: WindowWithDataLayer;

const _sendEvent = (data: object) => {
  const dataLayer = window.dataLayer as any[];

  if (!Array.isArray(dataLayer)) {
    return;
  }

  dataLayer.push(data);
};

const sendGoogleAnalyticsEvent = (event: AnalyticsEvent) => {
  const { name, value } = event;

  _sendEvent({
    event: "analytics",
    event_type: name,
    ...value,
  });
};

const setUser = (userId: string, email?: string) =>
  _sendEvent({
    email,
    event: "setUserID",
    user_id: userId,
  });

const resetUser = () => _sendEvent({ email: undefined, event: "resetUserID", user_id: undefined });

const googleTagManager = {
  resetUser,
  sendEvent: sendGoogleAnalyticsEvent,
  setUser,
};

export { googleTagManager };
