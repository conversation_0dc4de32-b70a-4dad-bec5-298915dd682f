import { AnalyticsEvent } from ".";

type WindowWithClarity = Window & {
  clarity: (...args: any[]) => void;
};

type AnalyticsEventWithClarity = AnalyticsEvent & {
  value: {
    clarity: boolean;
    error: boolean;
  };
};

declare const window: WindowWithClarity;

const _sendEvent = (...data: any[]) => {
  try {
    if (window?.clarity && typeof window.clarity === "function") {
      window.clarity(...data);
    }
  } catch (err) {
    console.error(err);
  }
};

const setUser = (userId: string, email?: string) => {
  _sendEvent("identify", userId, null, null, email);
};

const resetUser = () => _sendEvent("identify", null, null, null, null);

const clarity = {
  resetUser,
  sendEvent(event: AnalyticsEvent) {
    const { name, value } = event as AnalyticsEventWithClarity;

    if (!value?.clarity) {
      return;
    }

    if (value.error === true) {
      _sendEvent("set", "error", name); // custom tags
      _sendEvent("event", "error"); // groups all error
      _sendEvent("upgrade", "critical errors to be captured");
    }
    _sendEvent("event", name);
  },
  setUser,
};

export { clarity };
