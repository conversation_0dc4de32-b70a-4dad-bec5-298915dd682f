// import { setContext, captureException, setUser } from "@sentry/react";

// import { AnalyticsEvent } from ".";
// // import { ENV } from "app/utils/constant";

// // import { getDebugInfo } from "./debug-info";

// /**
//  * Enhance Sentry with additional context data like clarity ID
//  */
// // export const enhanceSentryContext = () => {
// //   const debugInfo = getDebugInfo();

// //   // Add clarity ID as a tag if available
// //   if (debugInfo.clarityUserId) {
// //     Sentry.setTag("clarityUserId", debugInfo.clarityUserId);
// //   }

// //   // Add clarity session ID as a tag if available
// //   if (debugInfo.claritySessionId) {
// //     Sentry.setTag("claritySessionId", debugInfo.claritySessionId);
// //   }

// //   // Add trace ID if available
// //   if (debugInfo.traceId) {
// //     Sentry.setTag("customTraceId", debugInfo.traceId);
// //   }
// // };

// // /**
// //  * Capture an error with enhanced context
// //  * @param error The error to capture
// //  * @param additionalData Additional context data to include
// //  */
// // export const captureErrorWithContext = (
// //   error: unknown,
// //   additionalData?: Record<string, unknown>
// // ) => {
// //   enhanceSentryContext();

// //   if (additionalData) {
// //     setContext("additionalData", additionalData);
// //   }

// //   captureException(error);
// // };

// // /**
// //  * Set the current user in Sentry
// //  * @param userId User ID
// //  * @param email User email
// //  */
// // export const setSentryUser = (userId: string, email?: string) => {
// //   setUser({ email, id: userId });
// //   enhanceSentryContext();
// // };

// /**
//  * Reset the user in Sentry (typically used during logout)
//  */
// export const resetSentryUser = () => {
//   SentrysetUser(null);
// };

// type AnalyticsEventWithSentry = AnalyticsEvent & {
//   value: {
//     [key: string]: any;
//     error?: boolean;
//     sentry?: boolean;
//   };
// };

// const isProduction = import.meta.env.MODE === "production";

// const _sendEvent = (
//   message: string,
//   level: "fatal" | "error" | "warning" | "log" | "info" | "debug" = "info",
//   data?: Record<string, unknown>
// ): void => {
//   if (!isProduction) return;

//   try {
//     // Add context data including clarity ID
//     enhanceSentryContext();

//     captureMessage(message, { level });
//     if (data) {
//       setContext("event_data", data);
//     }
//   } catch (err) {
//     console.error("Error sending event to Sentry:", err);
//   }
// };

// const sentry = {
//   resetUser: () => {
//     if (!isProduction) return;
//     resetSentryUser();
//   },
//   sendEvent: (event: AnalyticsEvent) => {
//     const { name, value } = event as AnalyticsEventWithSentry;

//     if (!value?.sentry || !isProduction) {
//       return;
//     }

//     if (value.error) {
//       _sendEvent(`Critical error: ${name}`, "error", value);
//     } else {
//       _sendEvent(`Event: ${name}`, "info", value);
//     }
//   },
//   setUser: (userId: string, email?: string) => {
//     if (!isProduction) return;
//     setSentryUser(userId, email);
//   },
// };

// export { sentry };
