import { clarity } from "./clarity";
import { googleTagManager } from "./google-tag-manager";

interface AnalyticsEventProvider {
  resetUser: () => void;
  sendEvent?: (event: AnalyticsEvent) => void;
  setUser: (userId: string, email?: string) => void;
}

export type AnalyticsEvent = {
  name: string;
  value: object;
};

const analyticsProviders: AnalyticsEventProvider[] = [googleTagManager, clarity];
const analyticsProvidersForIdentity = [...analyticsProviders];

const sendAnalyticsEvent = (
  event: AnalyticsEvent,
  providers: AnalyticsEventProvider[] = analyticsProviders,
) => {
  providers.forEach((provider) => {
    try {
      provider?.sendEvent?.(event);
    } catch (e) {
      // TODO: handle error
      console.error(e);
    }
  });
};

const setUser = (
  userId: string,
  email?: string,
  providers: AnalyticsEventProvider[] = analyticsProvidersForIdentity,
) => {
  providers.forEach((provider) => {
    try {
      provider.setUser(userId, email);
    } catch (e) {
      // TODO: handle error
      console.error(e);
    }
  });
};

const resetUser = (providers: AnalyticsEventProvider[] = analyticsProvidersForIdentity) => {
  providers.forEach((provider) => {
    try {
      provider.resetUser();
    } catch (e) {
      // TODO: handle error
      console.error(e);
    }
  });
};

export { sendAnalyticsEvent, setUser, resetUser };
