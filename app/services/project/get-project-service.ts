import { Container } from "inversify";
import { ProjectService } from "./project";

export async function getProjectService(container: Container) {
  let projectServicePromise = null;

  if (container.isBound(ProjectService)) {
    return container.get(ProjectService);
  }

  if (!projectServicePromise) {
    projectServicePromise = import("./project-impl").then(({ ProjectServiceImpl }) => {
      container.bind(ProjectService).to(ProjectServiceImpl).inSingletonScope();

      return container.get(ProjectService);
    });
  }

  return projectServicePromise;
}
