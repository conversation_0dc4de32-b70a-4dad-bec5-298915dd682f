import { inject, injectable } from "inversify";

import { StrapiService } from "../strapi";

import { TranslationService } from "./translation";
import { Translation } from "./types";
@injectable()
export class TranslationServiceImpl implements TranslationService {
  constructor(
    @inject<StrapiService>(StrapiService)
    private readonly StrapiService: StrapiService,
  ) {}

  async getTranslation(locale: string, namespace: string): Promise<Translation> {
    const data = await this.StrapiService.strapiAxios.get("/translations", {
      params: {
        filters: {
          namespace: {
            $eqi: namespace,
          },
        },
        locale,
      },
    });
    return data?.data[0] ?? null;
  }
}
