import { RestError, RestSuccess } from "~/services/rest-helper";

import { ProductListImpl } from "./product-list-impl";

export enum GetProductListErrorCodes {
  TBD = "TBD",
}

export type Product = {
  buffer_time_after: string;
  buffer_time_before: string;
  categories: string[];
  combinations: unknown[];
  created_date: string;
  cross_sell_groups: unknown[];
  custom_attributes: unknown[];
  description: string;
  duration_range: Record<string, unknown>;
  durations: unknown[];
  id: number;
  images: string[];
  interval: string;
  is_bookable: boolean;
  is_large_size: boolean;
  is_popular: boolean;
  manage_stock: boolean;
  marketplace_categories: Array<{
    id: number;
    title: string;
  }>;
  marketplace_product: {
    status: string;
    status_display: string;
    code: number;
    note: string;
  };
  max_future_booking: number;
  minimum_notice: number | null;
  minimum_notice_unit: string;
  minimum_notice_unit_display: string;
  modifier_groups: unknown[];
  operating_hours: unknown[];
  per_item_quantity: number;
  per_item_type: {
    value: string;
    label: string;
  };
  price: string;
  price_range: {
    min: string;
    max: string;
  };
  sale_price: string;
  sale_price_range: {
    min: string;
    max: string;
  };
  share_link: string;
  sku: string;
  slug: string;
  status: string;
  stock_quantity: number;
  title: string;
  updated_date: string;
  variants: unknown[];
  weight: number | null;
  weight_unit: {
    value: string;
    label: string;
  };
  youtube_link: string;
};

export type GetProductListResponseData = {
  count: number;
  next: string | null;
  previous: string | null;
  results: Product[];
};

export type GetProductListResponse =
  | RestSuccess<GetProductListResponseData>
  | RestError<GetProductListErrorCodes>;

export type ProductService = ProductListImpl;
export const ProductService = Symbol("ProductService");
