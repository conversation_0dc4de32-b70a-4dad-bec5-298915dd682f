import { ServerEnvService } from "./env";
import { commonEnvServiceImpl } from "./env-impl.common";

/**
 * Server-side environment variables implementation
 * Extends the common implementation with server-specific variables
 */
export const serverEnvServiceImpl: ServerEnvService = {
  ...commonEnvServiceImpl,
  // CSP (Content Security Policy) variables
  CSP_UPGRADE_INSECURE_REQUESTS: import.meta.env.VITE_CSP_UPGRADE_INSECURE_REQUESTS || "",
  CSP_DEFAULT_SRC: import.meta.env.VITE_CSP_DEFAULT_SRC || "",
  CSP_SCRIPT_SRC: import.meta.env.VITE_CSP_SCRIPT_SRC || "",
  CSP_BASE_URI: import.meta.env.VITE_CSP_BASE_URI || "",
  CSP_FRAME_ANCESTORS: import.meta.env.VITE_CSP_FRAME_ANCESTORS || "",
  CSP_FRAME_SRC: import.meta.env.VITE_CSP_FRAME_SRC || "",
  CSP_OBJECT_SRC: import.meta.env.VITE_CSP_OBJECT_SRC || "",
  CSP_FORM_ACTION: import.meta.env.VITE_CSP_FORM_ACTION || "",
  CSP_CONNECT_SRC: import.meta.env.VITE_CSP_CONNECT_SRC || "",
  CSP_IMG_SRC: import.meta.env.VITE_CSP_IMG_SRC || "",
  CSP_STYLE_SRC: import.meta.env.VITE_CSP_STYLE_SRC || "",
  CSP_FONT_SRC: import.meta.env.VITE_CSP_FONT_SRC || "",
  CSP_SCRIPT_SRC_ATTR: import.meta.env.VITE_CSP_SCRIPT_SRC_ATTR || "",
  CSP_SCRIPT_SRC_ELEM: import.meta.env.VITE_CSP_SCRIPT_SRC_ELEM || "",
  CSP_SCRIPT_SRC_NONCE: import.meta.env.VITE_CSP_SCRIPT_SRC_NONCE || "",
  CSP_SCRIPT_SRC_NONCE_ATTR: import.meta.env.VITE_CSP_SCRIPT_SRC_NONCE_ATTR || "",
  CSP_SCRIPT_SRC_NONCE_ELEM: import.meta.env.VITE_CSP_SCRIPT_SRC_NONCE_ELEM || "",
  // HSTS (HTTP Strict Transport Security) variables
  HSTS_MAX_AGE: import.meta.env.VITE_HSTS_MAX_AGE || "",
  HSTS_INCLUDE_SUBDOMAINS: import.meta.env.VITE_HSTS_INCLUDE_SUBDOMAINS || "",
  HSTS_PRELOAD: import.meta.env.VITE_HSTS_PRELOAD || "",
  // Other security headers
  REFERRER_POLICY: import.meta.env.VITE_REFERRER_POLICY || "",
  CROSS_ORIGIN_RESOURCE_POLICY: import.meta.env.VITE_CROSS_ORIGIN_RESOURCE_POLICY || "",
  X_CONTENT_TYPE_OPTIONS: import.meta.env.VITE_X_CONTENT_TYPE_OPTIONS || "",
  X_DNS_PREFETCH_CONTROL: import.meta.env.VITE_X_DNS_PREFETCH_CONTROL || "",
  X_XSS_PROTECTION: import.meta.env.VITE_X_XSS_PROTECTION || "",
  X_FRAME_OPTIONS: import.meta.env.VITE_X_FRAME_OPTIONS || "",
};
