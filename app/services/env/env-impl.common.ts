import { CommonEnvService, Mode } from "./env";

/**
 * Common environment variables implementation
 * This contains variables that are available in both client and server contexts
 */
export const commonEnvServiceImpl: CommonEnvService = {
  MODE: import.meta.env.MODE as Mode,
  STRAPI_TOKEN: import.meta.env.VITE_STRAPI_TOKEN || "",
  STRAPI_URL: import.meta.env.VITE_STRAPI_URL || "",
  APP_API_URL: import.meta.env.VITE_APP_API_URL || "",
  SHOP_PRODUCT_TOKEN: import.meta.env.VITE_SHOP_PRODUCT_TOKEN || "",
  PLATFORM: import.meta.env.VITE_PLATFORM || "",
  FR_R_AM_URL: import.meta.env.VITE_FR_R_AM_URL || "",
  FR_R_OAUTH_CLIENT: import.meta.env.VITE_FR_R_OAUTH_CLIENT || "",
  FR_R_OAUTH_SCOPE: import.meta.env.VITE_FR_R_OAUTH_SCOPE || "",
  FR_R_REALM_PATH: import.meta.env.VITE_FR_R_REALM_PATH || "",
  FR_TIMEOUT: Number(import.meta.env.VITE_FR_TIMEOUT) || 5000,
  SHOPBOXO_URL: import.meta.env.VITE_SHOPBOXO_URL || "",
  AUTH_TYPE: import.meta.env.VITE_AUTH_TYPE || "",
};
