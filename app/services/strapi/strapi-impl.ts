import axios, { AxiosInstance } from "axios";
import { injectable, inject } from "inversify";

import { EnvService } from "../env";
import { LocaleService } from "../locale";

import { StrapiService } from "./strapi";

@injectable()
export class StrapiImpl implements StrapiService {
  strapiAxios: AxiosInstance;

  constructor(
    @inject(EnvService)
    private readonly envSvc: EnvService,

    @inject(LocaleService)
    private readonly localeSvc: LocaleService,
  ) {
    this.strapiAxios = axios.create({
      baseURL: this.envSvc.STRAPI_URL,
      headers: {
        Authorization: `Bearer ${this.envSvc.STRAPI_TOKEN}`,
      },
    });
    this.strapiAxios.interceptors.request.use((req) => {
      req.params = {
        ...req.params,
        // override only when locale is falsy
        locale: req.params?.locale || this.localeSvc.getLang(),
      };
      return req;
    });
    this.strapiAxios.interceptors.response.use((res) => res.data);
  }
}
