/* eslint-disable @typescript-eslint/no-explicit-any */
import type { Axios, AxiosRequestConfig, AxiosResponse } from "axios";

export type HttpRequestConfig<D = any> = AxiosRequestConfig<D> & {
  timeout?: number;
};

export type HttpResponse<T = any, D = any> = AxiosResponse<T, D>;

export type Events = {
  unauthenticated: void;
};

export interface HttpClient {
  readonly axios: Axios;

  delete<T = any, R = HttpResponse<T>, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<R>;
  get<T = any, R = HttpResponse<T>, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<R>;
  head<T = any, R = HttpResponse<T>, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<R>;
  options<T = any, R = HttpResponse<T>, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<R>;
  patch<T = any, R = HttpResponse<T>, D = any>(
    url: string,
    data?: D,
    config?: HttpRequestConfig<D>,
  ): Promise<R>;
  post<T = any, R = HttpResponse<T>, D = any>(
    url: string,
    data?: D,
    config?: HttpRequestConfig<D>,
  ): Promise<R>;
  put<T = any, R = HttpResponse<T>, D = any>(
    url: string,
    data?: D,
    config?: HttpRequestConfig<D>,
  ): Promise<R>;
  request<T = any, R = HttpResponse<T>, D = any>(config: HttpRequestConfig<D>): Promise<R>;
}

export interface HttpClientFactory {
  /**
   * Create authorized http clients for accessing protected resources.
   */
  create(config?: HttpRequestConfig): HttpClient;

  off: <E extends keyof Events>(event: E, listener: (event: Events[E]) => void) => this;

  on: <E extends keyof Events>(event: E, listener: (event: Events[E]) => void) => this;
}

export const HttpClientFactory = Symbol("HttpClientFactory");
