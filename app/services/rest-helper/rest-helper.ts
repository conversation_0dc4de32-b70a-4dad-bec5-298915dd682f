/* eslint-disable @typescript-eslint/no-explicit-any */
import type { Axios } from "axios";
import type { SetNonNullable } from "type-fest";

import { HttpRequestConfig } from "../http-client-factory";

export type PollingArgs<R = any, D = any> = SetNonNullable<
  HttpRequestConfig<D> & {
    pollingInterval?: number;
    pollingTimeout?: number;
    predicate: (response: R) => boolean;
  },
  "url"
>;

//! TODO: check if needed later
export const RestOKCode = "00.00.000";
export type RestOKCode = typeof RestOKCode;

const RestOKPattern = /^\d{2}\.\d{2}\.[0-2]\d{2}$/;

//! TODO: update this to use the new code
export const isRestOkCode = (code: string) => RestOKPattern.test(code);

export type RestSuccess<Data = undefined> = {
  code: RestOKCode;
  data: Data;
};

export enum CommonRestErrorCodes {
  SERVER_ERROR = "SERVER_ERROR",
  UNKNOWN_ERROR = "UNKNOWN_ERROR",
}

export const PollingTimeoutError = "POLLING_TIMEOUT_ERROR";
export type PollingTimeoutError = typeof PollingTimeoutError;

export type RestErrorCode = Exclude<string, RestOKCode>;

export interface RestError<Code extends RestErrorCode = CommonRestErrorCodes.UNKNOWN_ERROR> {
  code: Code | CommonRestErrorCodes;
  innerError?: unknown;
  message?: string;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export type ExtractRestSuccess<T> = T extends RestSuccess<infer R> | RestError<infer E>
  ? Extract<T, RestSuccess<R>>
  : never;

/**
 * RestHelper helps
 * - Transforming success/error response to RestSuccess/RestError which is BE's response standard format
 * - Refreshing/Injecting token to request header
 */
export interface RestHelper {
  readonly axios: Axios;

  delete<R, E extends RestErrorCode = CommonRestErrorCodes.SERVER_ERROR, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<RestSuccess<R> | RestError<E>>;

  get<R, E extends RestErrorCode = CommonRestErrorCodes.SERVER_ERROR, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<RestSuccess<R> | RestError<E>>;

  head<R, E extends RestErrorCode = CommonRestErrorCodes.SERVER_ERROR, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<RestSuccess<R> | RestError<E>>;

  patch<R, E extends RestErrorCode = CommonRestErrorCodes.SERVER_ERROR, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<RestSuccess<R> | RestError<E>>;

  poll<R, E extends RestErrorCode = CommonRestErrorCodes.SERVER_ERROR, D = any>(
    pollingArgs: PollingArgs<R, D>,
  ): Promise<RestSuccess<R> | RestError<E | PollingTimeoutError>>;

  post<R, E extends RestErrorCode = CommonRestErrorCodes.SERVER_ERROR, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<RestSuccess<R> | RestError<E>>;

  put<R, E extends RestErrorCode = CommonRestErrorCodes.SERVER_ERROR, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<RestSuccess<R> | RestError<E>>;

  unwrap<R, E extends string>(promise: Promise<RestSuccess<R> | RestError<E>>): Promise<R>;
}

export const RestHelper = Symbol("RestHelper");
