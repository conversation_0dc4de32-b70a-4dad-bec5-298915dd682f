import { RestSuccess, RestError, RestOKCode } from "./rest-helper";

export const isRestSuccess = <R, E extends string>(
  response?: RestSuccess<R> | RestError<E>,
): response is RestSuccess<R> => {
  return response?.code === RestOKCode;
};

export const isRestError = <R, E extends string>(
  response?: RestSuccess<R> | RestError<E>,
): response is RestError<E> => {
  return response?.code !== RestOKCode;
};
