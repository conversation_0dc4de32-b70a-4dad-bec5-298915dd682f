import {
  StepOptions,
  FRStep,
  FRLoginSuccess,
  FRLoginFailure,
  FRCallbackFactory,
  LoggerFunctions,
} from "@forgerock/javascript-sdk";
import {
  RequestMiddleware,
  ServerConfig,
  TokenStoreObject,
} from "@forgerock/javascript-sdk/src/config/interfaces";
import { JwtPayload } from "jwt-decode";
import { Merge } from "type-fest";
import { LogLevel } from "vite";

export type AccessTokenPayload = JwtPayload & {
  crmContactCreated: boolean;
  crmLeadCreated: boolean;
  nafathVerified: boolean;
  passwordConfigured: boolean;
  scope: string[];
  userName: string;
};

export type GetTokensArgs = {
  forceRenew?: boolean;
};

export type ConfigJourneyArg<T> = {
  journeyMap?: Partial<T>;
  prefix?: string;
};

export type NextStepOption<T> = Merge<
  StepOptions,
  {
    journey?: T;
    tree?: string;
  }
>;

export type AuthHasNextStep<T extends string = string> = Merge<
  FRStep,
  {
    getStage(): T;
  }
>;

export type AuthStep<T extends string = string> =
  | AuthHasNextStep<T>
  | FRLoginSuccess
  | FRLoginFailure;

export interface ConfigOptions {
  callbackFactory?: FRCallbackFactory;
  clientId?: string;
  middleware?: RequestMiddleware[];
  realmPath?: string;
  redirectUri?: string;
  scope?: string;
  serverConfig?: ServerConfig;
  tokenStore?: TokenStoreObject | "sessionStorage" | "localStorage";
  tree?: string;
  type?: string;
  oauthThreshold?: number;
  logLevel?: LogLevel;
  logger?: LoggerFunctions;
  prefix?: string;
}
