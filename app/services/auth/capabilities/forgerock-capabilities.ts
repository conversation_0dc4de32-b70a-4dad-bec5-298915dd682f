import { FRCallback } from "@forgerock/javascript-sdk";

import { JourneyMap, JourneyName } from "../implementations/forge-rock/journey-map";

import {
  AccessTokenPayload,
  AuthStep,
  ConfigJourneyArg,
  GetTokensArgs,
  NextStepOption,
} from "./types";

export interface ForgeRockCapabilities {
  getTokens(args?: GetTokensArgs): Promise<
    | {
        accessToken: string;
        idToken?: string;
        refreshToken?: string;
      }
    | undefined
  >;

  decodeAccessToken(accessToken: string): AccessTokenPayload;
  getDecodedAccessToken(args?: GetTokensArgs): Promise<AccessTokenPayload | undefined>;

  configJourneys(arg: ConfigJourneyArg<JourneyMap>): this;

  next<T extends AuthStep>(prev?: T, option?: NextStepOption<JourneyName>): Promise<T>;

  getFailedPolicies(callbacks: FRCallback[]): (string | undefined)[];
}
