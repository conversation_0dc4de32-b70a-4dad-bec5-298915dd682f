import { inject, injectable } from "inversify";

import { EnvService } from "~/services/env";
import { HttpClientFactory } from "~/services/http-client-factory";
import { removeItem, setItem } from "~/utils/storage-helpers";

import { AuthService } from "../../auth";
import { useStore } from "~/store/store";

@injectable()
export class ShopboxoAuthImpl implements AuthService {
  public readonly useProfileStore: any;
  private readonly shopboxoHttpClient;

  constructor(
    @inject(HttpClientFactory)
    private readonly httpClientFactory: HttpClientFactory,

    @inject(EnvService)
    private readonly envService: EnvService,
  ) {
    this.shopboxoHttpClient = this.httpClientFactory.create({
      baseURL: this.envService.SHOPBOXO_URL,
    });
  }

  async signIn(args: { username: string; password: string }): Promise<void> {
    const res = await this.shopboxoHttpClient.post("marketplaces/login/", args);
    setItem("accessToken", res.data.token);
    setItem("refreshToken", res.data.refresh_token);

    return res.data;
  }

  async refreshTokens(): Promise<unknown> {
    return new Promise((resolve) => {
      resolve(undefined);
    });
  }

  async signOut(): Promise<void> {
    removeItem("accessToken");
    removeItem("refreshToken");
    useStore().getState().setSignedIn(false);
  }
}
