import type {
  <PERSON><PERSON><PERSON>back,
  F<PERSON>allback,
  FRStep,
  TextOutputCallback,
} from "@forgerock/javascript-sdk";
import { CallbackType, FRPolicy } from "@forgerock/javascript-sdk";
import { ForgeRockBridge } from "@roshn/shared/forgerock-bridge";
import { inject, injectable } from "inversify";
import { jwtDecode } from "jwt-decode";
import { PartialDeep } from "type-fest";

import { EnvService } from "~/services/env";
import { LocaleService } from "~/services/locale";
import { useStore } from "~/store/store";

import { AuthService } from "../../auth";
import { ForgeRockCapabilities } from "../../capabilities/forgerock-capabilities";
import {
  AccessTokenPayload,
  ConfigJourneyArg,
  GetTokensArgs,
  NextStepOption,
} from "../../capabilities/types";

import { AuthHasNextStep, AuthStep, isHasNextStep } from "./auth-steps";
import { DefaultJourneyMap, JourneyMap, JourneyName } from "./journey-map";

export type Jwt = {
  mail: string;
  sub: string;
  userName: string;
};

type SignedInState = {
  signedIn: true;
  sub: string;
  updatedPhoneNumber: boolean;
  userName: string;
};

type SignedOutState = {
  signedIn: false;
};

export type AuthState = SignedInState | SignedOutState;

@injectable()
export class FRAuthServiceImpl implements AuthService, ForgeRockCapabilities {
  static readonly CollectingDeviceInfoStepName = "CollectDeviceInfo";
  static readonly LocalePrompt = "Locale";
  static readonly PlatformPrompt = "Platform";

  protected _journeyMap: JourneyMap = { ...DefaultJourneyMap };

  protected readonly frPolicy = FRPolicy;

  constructor(
    @inject(ForgeRockBridge)
    private readonly frBridge: ForgeRockBridge,

    @inject(LocaleService)
    private readonly localeSvc: LocaleService,

    @inject(EnvService)
    private readonly envSvc: EnvService,
  ) {}

  signOut = async () => {
    try {
      await this.frBridge.logout();
      // Clear localStorage completely
      localStorage.clear();

      // Clear browser cache using cache API if available
      if (window.caches) {
        try {
          const cacheKeys = await window.caches.keys();
          await Promise.all(cacheKeys.map((key) => window.caches.delete(key)));
        } catch (error) {
          console.error("Error clearing cache:", error);
        }
      }

      // Clear cookies
      const cookies = document.cookie.split(";");
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i];
        const eqPos = cookie.indexOf("=");
        const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      }

      // Clear session storage
      sessionStorage.clear();

      // Reload the page to ensure a fresh state
      window.location.reload();
    } catch (err) {
      console.error("Failed to logout", err);
    }

    useStore().getState().setSignedIn(false);
    useStore().getState().setProfile(undefined);
  };

  signIn = async () => {
    const tokens = await this.getTokens({
      forceRenew: false,
    });

    if (!tokens) {
      this.signOut();
      throw new Error("No tokens");
    }

    const jwt = jwtDecode<PartialDeep<Jwt>>(tokens.accessToken);

    if (!jwt || !jwt.userName || !jwt.sub || !jwt.mail) {
      this.signOut();
      throw new Error("Invalid jwt");
    }

    useStore().setState({
      signedIn: true,
      sub: jwt.sub,
      updatedPhoneNumber: false,
      userName: jwt.userName,
    } satisfies SignedInState);
  };

  async refreshTokens() {
    return this.getTokens({
      forceRenew: true,
    });
  }

  configJourneys({ journeyMap, prefix }: ConfigJourneyArg<JourneyMap>): this {
    const next = {
      ...this._journeyMap,
      ...journeyMap,
    };

    if (prefix) {
      Object.keys(next).forEach((_key) => {
        const key = _key as JourneyName;

        next[key] = `${prefix}/${next[key]}`;
      });
    }

    this._journeyMap = next;

    return this;
  }

  async next<T extends AuthStep>(
    prev?: T | undefined,
    option?: NextStepOption<JourneyName> | undefined,
  ): Promise<T> {
    const { journey, ...frOption } = {
      ...option,
    };

    if (journey) {
      frOption.tree = this._journeyMap[journey] ?? frOption.tree;
    }

    let nextStep;
    try {
      nextStep = (await this.frBridge.next(prev as FRStep, frOption)) as AuthStep;
    } catch (err) {
      console.error("Failed to get next step", err);
      throw err;
    }

    /**
     * Automatically submit locale if the current journey requires it
     */
    nextStep = await this.setDeviceInfoIfNeeded(nextStep);

    return nextStep as T;
  }

  async getTokens({ forceRenew = false }: GetTokensArgs = {}) {
    try {
      return await this.frBridge.getTokens({
        forceRenew,
      });
    } catch (err) {
      console.error("Failed to get tokens", err);
    }
  }

  decodeAccessToken(accessToken: string) {
    return jwtDecode<AccessTokenPayload>(accessToken);
  }

  async getDecodedAccessToken(args?: GetTokensArgs) {
    const tokens = await this.getTokens(args);

    if (!tokens?.accessToken) return;

    return this.decodeAccessToken(tokens.accessToken);
  }

  getFailedPolicies(callbacks: FRCallback[]): (string | undefined)[] {
    const failedPolicies = callbacks
      .flatMap((cb) => {
        const property = cb.getOutputByName("prompt", "Property");
        const failedPolicies = cb.getOutputByName<any[] | undefined>("failedPolicies", undefined);
        if (!failedPolicies?.length) return;

        console.error("Failed FR policies: ", failedPolicies);
        const customMessageCreator = {
          //TODO: add locale messages
        };

        return this.frPolicy.parseFailedPolicyRequirement(
          {
            policyRequirements: failedPolicies.map((p) => JSON.parse(p)),
            property,
          },
          customMessageCreator,
        );
      })
      .filter(Boolean);

    return failedPolicies;
  }

  private setChoiceValue(frStep: AuthHasNextStep, prompt: string, choiceValue: string) {
    const matchedCallback = frStep
      .getCallbacksOfType<ChoiceCallback>(CallbackType.ChoiceCallback)
      .find((cb) => {
        if (cb.getPrompt() === prompt) {
          return true;
        }

        return false;
      }) as ChoiceCallback;

    if (!matchedCallback) throw new Error(`No choice callback found for prompt: ${prompt}`);

    matchedCallback.setChoiceValue(choiceValue);
  }

  private static isCollectingDeviceInfoStage(frStep?: AuthStep): frStep is AuthHasNextStep {
    if (!isHasNextStep(frStep)) return false;

    return (
      frStep
        .getCallbacksOfType<TextOutputCallback>(CallbackType.TextOutputCallback)?.[0]
        ?.getMessage() === FRAuthServiceImpl.CollectingDeviceInfoStepName
    );
  }

  /**
   * Looking for locale callback and set user's locale to it
   */
  private setDeviceInfoIfNeeded(frStep: AuthStep): Promise<AuthStep>;
  private setDeviceInfoIfNeeded(frStep?: AuthStep): Promise<AuthStep | undefined>;
  private async setDeviceInfoIfNeeded(frStep?: AuthStep): Promise<AuthStep | undefined> {
    if (!FRAuthServiceImpl.isCollectingDeviceInfoStage(frStep)) return frStep;

    const locale = this.localeSvc.getLocale()?.toLowerCase();
    this.setChoiceValue(frStep, FRAuthServiceImpl.LocalePrompt, locale);
    this.setChoiceValue(frStep, FRAuthServiceImpl.PlatformPrompt, this.envSvc.PLATFORM);

    return await this.next(frStep as AuthStep);
  }
}
