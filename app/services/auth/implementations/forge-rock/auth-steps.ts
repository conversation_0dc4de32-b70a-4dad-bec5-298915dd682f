import { FRStep, FRLoginSuccess, FRLoginFailure } from "@forgerock/javascript-sdk";
import { Merge } from "type-fest";

export type AuthHasNextStep<T extends string = string> = Merge<
  FRStep,
  {
    getStage(): T;
  }
>;

export const isHasNextStep = (step?: AuthStep | FRStep): step is AuthHasNextStep =>
  !!step && !!(step as unknown as FRStep).getStage;

export type AuthStep<T extends string = string> =
  | AuthHasNextStep<T>
  | FRLoginSuccess
  | FRLoginFailure;

export enum AuthStage {
  NinQuestion = "NinQuestion",
  Otp = "Otp",
  Password = "Password",
  PhoneNumber = "PhoneNumber",
  Profile = "Profile",
  Resend = "Resend",
  ResetPassword = "ResetPassword",
}
export enum SAAuthStage {
  EditPhoneNumber = "EditPhoneNumber",
  OAuth = "OAuth",
  Otp = "Otp",
  PhoneNumber = "telephoneNumber",
  Resend = "Resend",
}

export type SAPhoneNumberStep = AuthHasNextStep<SAAuthStage.PhoneNumber>;
export type SAOAuthStep = AuthHasNextStep<SAAuthStage.OAuth>;

export type SignUpPhoneNumberStep = AuthHasNextStep<AuthStage.PhoneNumber>;
export type SignUpOtpStep = AuthHasNextStep<AuthStage.Otp>;
export type SignUpResendStep = AuthHasNextStep<AuthStage.Resend>;
export type SignUpPasswordStep = AuthHasNextStep<AuthStage.Password>;
export type SignUpProfileStep = AuthHasNextStep<AuthStage.Profile>;
export type SignUpNinQuestionStep = AuthHasNextStep<AuthStage.NinQuestion>;
export type SignUpResetPasswordStep = AuthHasNextStep<AuthStage.ResetPassword>;

export const isSignUpOtpStep = (step?: FRStep | AuthStep): step is SignUpOtpStep => {
  return !!step && isHasNextStep(step) && step.getStage() === AuthStage.Otp;
};

export const isSignUpResendStep = (step?: FRStep | AuthStep): step is SignUpResendStep => {
  return !!step && isHasNextStep(step) && step.getStage() === AuthStage.Resend;
};

export const isSignUpProfileStep = (step?: FRStep | AuthStep): step is SignUpProfileStep => {
  return !!step && isHasNextStep(step) && step.getStage() === AuthStage.Profile;
};

export const isSignUpPasswordStep = (step?: FRStep | AuthStep): step is SignUpPasswordStep => {
  return !!step && isHasNextStep(step) && step.getStage() === AuthStage.Password;
};

export type SignUpStep = AuthStep<AuthStage>;
export type SASignInStep = AuthStep<SAAuthStage>;

export enum UpdatePhoneNumberStage {
  ExistedPhoneNumber = "ExistedPhoneNumber",
  Otp = "Otp",
  PhoneNumber = "PhoneNumber",
  Resend = "Resend",
}

export type NewPhoneNumberStep = AuthHasNextStep<UpdatePhoneNumberStage.PhoneNumber>;
export type NewPhoneNumberOtpStep = AuthHasNextStep<UpdatePhoneNumberStage.Otp>;
export type UpdatePhoneNumberResendOTPStep = AuthHasNextStep<UpdatePhoneNumberStage.Resend>;

export type UpdatePhoneNumberStep = AuthStep<UpdatePhoneNumberStage>;

export enum ConfigurePasswordStage {
  Password = "Password",
}

export type ConfigureEnterPasswordStep = AuthHasNextStep<ConfigurePasswordStage.Password>;
export type ConfigurePasswordStep = AuthStep<ConfigurePasswordStage>;

export type ResetPasswordStep = AuthStep<ConfigurePasswordStage>;
