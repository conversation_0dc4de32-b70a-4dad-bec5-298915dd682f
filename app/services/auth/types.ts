export interface NationalAddress {
  additionalNumber: string;
  buildingNumber: string;
  city: string;
  cityId: string;
  cityL2: string;
  district: string;
  districtID: string;
  districtL2: string;
  isPrimaryAddress: string;
  locationCoordinates: string;
  postCode: string;
  regionId: string;
  regionName: string;
  regionNameL2: string;
  streetL2: string;
  streetName: string;
  unitNumber: string;
}

export interface NafathProfile {
  PersonId: string;
  ServiceName: string;
  aud: string;
  dateOfBirthG: string;
  dateOfBirthH: string;
  englishFirstName: string;
  englishLastName: string;
  englishSecondName: string;
  englishThirdName: string;
  familyName: string;
  fatherName: string;
  firstName: string;
  gender: string;
  grandFatherName: string;
  id: string;
  idExpiryDate: string;
  idExpiryDateG: string;
  idIssueDate: string;
  idIssueDateG: string;
  idIssuePlace: string;
  idVersionNumber: number;
  iss: string;
  jti: string;
  lastTravelEntryDate: string;
  lastTravelEntryDateG: string;
  lastTravelExitDateG: string;
  logId: number;
  nationalAddress: NationalAddress[];
  nationality: string;
  nationalityCode: string;
  nin: string;
  occupationCode: string;
  passportNumber: string;
  placeOfBirth: string;
  socialStatusCode: number;
  socialStatusDesc: string;
  status: string;
  sub: string;
  totalNumberOfCurrentDependents: number;
  transId: string;
}

export interface FullUserProfile {
  _id: string;
  _rev: string;
  accountStatus: string;
  activateAccount?: string;
  activeDate?: string;
  aliasList: string[];
  city?: string;
  consentedMappings: string[];
  contactId: string;
  country?: string;
  crmContactCreated?: boolean;
  crmLeadCreated?: boolean;
  description?: string;
  displayName?: string;
  effectiveApplications: string[];
  effectiveAssignments: string[];
  effectiveGroups: string[];
  effectiveRoles: string[];
  expireAccount?: string;
  givenName: string;
  inactiveDate?: string;
  kbaInfo: string[];
  mail: string;
  memberOfOrgIDs: string[];
  nafath?: NafathProfile;
  nafathVerified?: boolean;
  organizationName?: string;
  passwordConfigured?: boolean;
  postalAddress?: string;
  postalCode?: string;
  preferences?: string;
  profileImage?: string;
  registryId: string;
  roshnStatus: string;
  sn: string;
  stateProvince?: string;
  telephoneNumber: string;
  userName: string;
  isValidNationalIdPresent?: boolean;
  isMailVerified?: boolean;
}

export type AccountData = {
  crmContactCreated: boolean;
  crmLeadCreated: boolean;
  email: string;
  firstName: string;
  lastName: string;

  nafathVerified: boolean;
  name: string;
  passwordConfigured: boolean;
  phoneNumber: string;
};

export type AccountState = AccountData | undefined;

export type ProfileState = FullUserProfile | undefined;

type SignedInState = {
  signedIn: true;
  sub: string;
  updatedPhoneNumber: boolean;
  userName: string;
};

type SignedOutState = {
  signedIn: false;
};

export type AuthState = SignedInState | SignedOutState;
