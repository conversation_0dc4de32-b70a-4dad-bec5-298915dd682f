import { useQuery } from "@tanstack/react-query";

import { QueryKey } from "../context/reactQueryProvider";
import { ProductService } from "../services/product-list/product-list";

import { useInjection } from "./use-di";

export type QueryProductListParams = {marketplace_product_status: string, search: string};

export function useProductList(args: QueryProductListParams) {
  const productService = useInjection<ProductService>(ProductService);

  return useQuery({
    queryKey: [QueryKey.PRODUCT_LIST, args],
    queryFn: () => productService.getProductList(args),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

export function useInterestList() {
  const productService = useInjection<ProductService>(ProductService);

  return useQuery({
    queryKey: [QueryKey.PRODUCT_LIST],
    queryFn: () => productService.getInterestList(),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
