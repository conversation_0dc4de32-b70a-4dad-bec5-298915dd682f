import { TFunction } from "i18next";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";

import { NAMESPACES, type SupportedLanguage } from "~/i18n/i18n-options";

export interface LocalizationHook {
  t: TFunction<"translation", undefined>;
  currentLanguage: SupportedLanguage;
  currentDirection: "ltr" | "rtl";
  changeLanguage: (lang: SupportedLanguage, marketplace?: string) => Promise<void>;
  isRTL: boolean;
  reloadTranslations: (marketplace: string) => Promise<void>;
}

export function useLocalization(): LocalizationHook {
  const { t, i18n } = useTranslation();

  const changeLanguage = useCallback(
    async (lang: SupportedLanguage) => {
      await i18n.changeLanguage(lang);
      document.documentElement.dir = i18n.dir();
      document.documentElement.lang = lang;
      document.body.setAttribute("data-direction", i18n.dir());
    },
    [i18n],
  );

  const reloadTranslations = useCallback(async () => {
    // Reload all namespaces for current language
    await i18n.reloadResources(i18n.language, NAMESPACES);
  }, [i18n]);

  return {
    t,
    currentLanguage: i18n.language as SupportedLanguage,
    currentDirection: i18n.dir(),
    changeLanguage,
    isRTL: i18n.dir() === "rtl",
    reloadTranslations,
  };
}
