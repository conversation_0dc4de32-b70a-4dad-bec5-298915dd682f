import { useMutation } from "@tanstack/react-query";

import { AuthService } from "~/interface/authTypes";

import { QueryKey } from "../context/reactQueryProvider";

import { useInjection } from "./use-di";

export function useLogin({
  onSuccess,
  onError,
}: {
  onSuccess?: ((data: unknown) => Promise<void> | void) | undefined;
  onError?: ((data: unknown) => Promise<void> | void) | undefined;
}) {
  const auth = useInjection<AuthService>(AuthService);

  return useMutation({
    mutationKey: [QueryKey.LOGIN],
    mutationFn: (args: { username: string; password: string }) => auth.signIn(args),
    onSuccess,
    onError,
  });
}
