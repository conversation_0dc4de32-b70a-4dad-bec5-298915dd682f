import { Control, useWatch } from "react-hook-form";
import { z } from "zod";

type StepId = string;
type FieldName = string;

export function useStepValidator(
  currentStep: StepId,
  control: Control<any>,
  schema: z.ZodObject<any>,
  stepFieldsMap: Record<StepId, FieldName[]>,
) {
  const values = useWatch({ control });

  const currentFields = stepFieldsMap[currentStep];

  const selectedValues = currentFields.reduce(
    (acc, key) => {
      acc[key] = values[key];
      return acc;
    },
    {} as Record<string, any>,
  );

  const stepSchema = schema.pick(Object.fromEntries(currentFields.map((k) => [k, true])));
  return stepSchema.safeParse(selectedValues).success;
}
