import { generatePath } from "@remix-run/react";
import { useCallback } from "react";

import { useInjection } from "~/hooks/use-di";
import { LocaleService } from "~/services/locale";

type _PathParam<Path extends string> = Path extends `${infer L}/${infer R}`
  ? _PathParam<L> | _PathParam<R>
  : Path extends `:${infer Param}`
    ? Param extends `${infer Optional}?`
      ? Optional
      : Param
    : never;

type PathParam<Path extends string> = Path extends "*" | "/*"
  ? "*"
  : Path extends `${infer Rest}/*`
    ? "*" | _PathParam<Rest>
    : _PathParam<Path>;

type ImpliedLocale<Path extends string> = Omit<Record<PathParam<Path>, string | number>, "locale">;

export function useAppPath() {
  const localeService = useInjection<LocaleService>(LocaleService);

  return useCallback(
    <Path extends string>(path: Path, params?: ImpliedLocale<Path>, hash?: string) => {
      const basePath = generatePath(path, {
        ...params,
        lang: localeService.getLang(),
      } as any);
      return hash ? `${basePath}#${hash}` : basePath;
    },
    [localeService],
  );
}
