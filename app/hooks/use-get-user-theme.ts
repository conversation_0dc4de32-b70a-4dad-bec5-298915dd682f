import { useQuery } from "@tanstack/react-query";

import { ThemeService } from "~/services/theme-service/theme-service";

import { QueryKey } from "../context/reactQueryProvider";

import { useInjection } from "./use-di";

export function useGetUserTheme() {
  const theme = useInjection<ThemeService>(ThemeService);

  return useQuery({
    queryKey: [QueryKey.THEME],
    queryFn: () => theme.getUserTheme(),
    experimental_prefetchInRender: true,
    enabled: false,
    select: (data) => data.palette,
  });
}
