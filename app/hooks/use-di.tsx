// Inspired by https://github.com/Kukkimonsuta/inversify-react
import type { Container, interfaces } from "inversify";
import { Container as ContainerConst } from "inversify";
import React from "react";

import { bindImplsCommon } from "~/di/bind-impls-common";

// Dependency Injection hooks

export type DIContextValue = {
  container: Container;
};

const UninitializedContext = Symbol("UninitializedContext");

const DIContext = React.createContext<DIContextValue | typeof UninitializedContext>(
  UninitializedContext,
);

export type DIProviderProps = React.PropsWithChildren<{
  container: Container;
}>;

export const DIProvider = ({ container, children }: DIProviderProps) => {
  if (!container) {
    throw new Error("DIProvider must be provided with a container");
  }

  const value = React.useMemo(() => ({ container }), [container]);

  return <DIContext.Provider value={value}>{children}</DIContext.Provider>;
};

export function useDIContainer() {
  const context = React.useContext(DIContext);

  if (context === UninitializedContext) {
    throw new Error("useDiContainer must be used within a DIProvider");
  }

  return context.container;
}

/**
 * Hook to get a dependency from the DI container
 */
export function useInjection<T>(identifier: interfaces.ServiceIdentifier<T>): T {
  const container = useDIContainer();

  const ref = React.useRef<T>();

  if (!ref.current) {
    ref.current = container.get<T>(identifier);
  }

  return ref.current;
}

export function getDIContainer(): Container {
  let container: Container | null = null;

  if (!container) {
    container = new ContainerConst();
    bindImplsCommon(container);
  }
  return container;
}
