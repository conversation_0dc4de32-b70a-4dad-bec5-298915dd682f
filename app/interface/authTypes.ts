import { FRAuthServiceImpl } from "~/services/auth/implementations/forge-rock/fr-auth-impl";
import { ShopboxoAuthImpl } from "~/services/auth/implementations/shopboxo/shopboxo-auth-impl";

export interface UserAuth {
  email: string;
  password: string;
}

export interface JWTToken {
  token: string;
  expiresIn: number;
}

export interface IAuthService {
  authenticateUser(userAuth: UserAuth): Promise<JWTToken>;
}

export type AuthService = FRAuthServiceImpl | ShopboxoAuthImpl;

export const AuthService = Symbol("AuthService");
