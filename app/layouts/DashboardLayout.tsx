import { css } from "@emotion/react";
import { AppTheme } from "@roshn/ui-kit";
import { ReactNode } from "react";

import { GlobalSidebar } from "~/features/global-sidebar/global-sidebar";

interface DashboardLayoutProps {
  children: ReactNode;
}

const styles = {
  container: css({
    display: "flex",
    height: "100vh",
  }),
  contentContainer: (theme: AppTheme) =>
    css({
      flex: "1",
      background: theme?.rds?.color.background.brand.secondary.inverse.default,
    }),
};

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div css={styles.container}>
      <GlobalSidebar />
      <div css={styles.contentContainer}>{children} </div>
    </div>
  );
}
