import { css } from "@emotion/react";
import { ReactNode } from "react";

import background from "~/assets/auth-background.svg?url";

interface PublicLayoutProps {
  children: ReactNode;
}

const styles = {
  background: css({
    backgroundImage: `url(${background})`,
    backgroundPosition: "center",
    minHeight: "100vh",
    backgroundRepeat: "no-repeat",
    backgroundSize: "cover",
  }),
};

export default function PublicLayout({ children }: PublicLayoutProps) {
  return (
    <div css={styles.background}>
      <main>{children}</main>
    </div>
  );
}
