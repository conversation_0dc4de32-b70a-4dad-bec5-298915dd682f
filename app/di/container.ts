import "reflect-metadata";
import { Container, interfaces } from "inversify";

/**
 * Create a inversify container with default options.
 * Default options are:
 * - defaultScope: "Singleton"
 */
export const createContainer = (containerOptions?: interfaces.ContainerOptions) => {
  const defaultOptions: interfaces.ContainerOptions = {
    defaultScope: "Singleton",
  };

  return new Container({ ...defaultOptions, ...containerOptions });
};
