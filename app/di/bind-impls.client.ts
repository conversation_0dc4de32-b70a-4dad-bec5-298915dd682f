import { Container } from "inversify";

import { ClientEnvService, EnvService } from "~/services/env";
import { clientEnvServiceImpl } from "~/services/env/env-impl.client";
import { LocaleService } from "~/services/locale";
import { localeServiceClientImpl } from "~/services/locale/locale-impl.client";

import { bindImplsCommon } from "./bind-impls-common";

export function bindImplsClient(container: Container) {
  bindImplsCommon(container);
  container.bind<LocaleService>(LocaleService).toConstantValue(localeServiceClientImpl);
  container.bind<ClientEnvService>(EnvService).toConstantValue(clientEnvServiceImpl);
}
