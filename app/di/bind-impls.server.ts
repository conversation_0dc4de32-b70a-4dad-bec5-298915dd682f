import { Container } from "inversify";

import { ServerEnvService, EnvService } from "~/services/env";
import { serverEnvServiceImpl } from "~/services/env/env-impl.server";
import { LocaleService } from "~/services/locale";
import { createLocaleService } from "~/services/locale/locale-impl.server";

import { bindImplsCommon } from "./bind-impls-common";

export function bindImplsServer(container: Container, lang: string) {
  bindImplsCommon(container);
  container.bind<LocaleService>(LocaleService).toConstantValue(createLocaleService(lang));
  container.bind<ServerEnvService>(EnvService).toConstantValue(serverEnvServiceImpl);
}
