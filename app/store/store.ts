import { getDIContainer } from "~/hooks/use-di";
import { PersistedStore, StoreFactory } from "~/services/store-factory";

import { AuthSlice, ProfileSlice, createAuthSlice, createProfileSlice } from "./slices";

let storeInstance: PersistedStore<AuthSlice & ProfileSlice> | null = null;

export function useStore(): PersistedStore<AuthSlice & ProfileSlice> {
  if (!storeInstance) {
    const storeFactory = getDIContainer().get<StoreFactory>(StoreFactory);

    storeInstance = storeFactory.createStore<AuthSlice & ProfileSlice>(
      (...a) => ({
        ...createAuthSlice(...a),
        ...createProfileSlice(...a),
      }),
      {
        persist: {
          name: "global-store",
          version: 0,
        },
      },
    );
  }

  return storeInstance;
}
