/**
 * Utility types for developing React components.
 */

import React from "react";

import { CommonProps } from "~/interface/common-props";

import { DistributiveOmit } from "./distributive";

export type BaseProps<P> = P & CommonProps;

export type AsChild = {
  asChild?: boolean;
};

/**
 * Override props of C which are defined in P.
 *
 * @example
 *
 * ```tsx
 *
 * // before
 *
 * type Props = React.ComponentProps<'input'> & { onChange: (e:Event, value:number) => void }
 * // => onChange is (e:Event) => void | (e:Event, value:number) => void
 *
 * // after
 * type Props = OverrideProps<'input', { onChange: () }>
 * // => onChange is (e:Event, value:number) => void
 * ```
 */
export type OverrideProps<C extends React.ElementType, P extends object = object> = BaseProps<P> &
  DistributiveOmit<React.ComponentPropsWithRef<C>, keyof BaseProps<P>>;

export type PolymorphicRef<C extends React.ElementType> = React.ComponentPropsWithRef<C>["ref"];

/**
 * Utility type for creating polymorphic components.
 *
 * @example
```tsx
// define props
export type TextProps<C extends React.ElementType> =
	PolymorphicComponentPropsWithRef<C, { size?: "sm" | "md" | "lg" }>;

// define component type
export type Text = <C extends React.ElementType = "span">(
	props: TextProps<C>,
) => React.ReactElement | null;

// define component
export const Text: Text = React.forwardRef(function Text<
	C extends React.ElementType = "span",
>({ component, ...props }: TextProps<C>, ref?: PolymorphicRef<C>) {
	const Comp = component ?? "span";

	return <Comp ref={ref} {...props} />;
});
```
 */
export type PolymorphicComponentPropsWithRef<
  C extends React.ElementType,
  P extends object = object,
> = OverrideProps<
  C,
  P & {
    component?: C;
    ref?: PolymorphicRef<C>;
  }
>;
