import { LoaderFunction } from "@remix-run/node";
import { Outlet } from "@remix-run/react";

import { bindImplsServer } from "~/di/bind-impls.server";
import { createContainer } from "~/di/container";
import { SupportedLanguage } from "~/i18n/i18n-options";
import { TranslationService } from "~/services/translation";

export const loader: LoaderFunction = async ({ params }) => {
  const { lang } = params;

  // NOTE: Below example shows usage of DI in loader
  const container = createContainer();
  bindImplsServer(container, lang as SupportedLanguage);
  container.get<TranslationService>(TranslationService);
  // const response = await svc.getTranslation("en", "home-acquisition");
  return {
    message: "Hello from loader",
  };
};
export default function Index() {
  return <Outlet />;
}
