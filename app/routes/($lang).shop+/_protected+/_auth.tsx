import { Outlet } from "@remix-run/react";
import { useEffect } from "react";

import { useThemeManager } from "~/context/coreProvider";
import { authenticationHoc } from "~/hoc/authentication";
import { useGetUserTheme } from "~/hooks/use-get-user-theme";
import { useGlobalAuthHandler } from "~/hooks/use-global-auth-handler";
import { AppPaths } from "~/utils/app-paths";

function ProtectedLayout() {
  const { applyUserTheme } = useThemeManager();
  const { refetch } = useGetUserTheme();

  useEffect(() => {
    refetch().then((res) => {
      const newTheme = {
        rds: {
          color: {
            background: {
              brand: {
                primary: {
                  // navbar:
                  //   res?.data?.brand_color ??
                  //   "linear-gradient(rgba(0, 40, 32, 1)100%,rgba(0, 60, 48, 1) 50%,rgba(0, 100, 80, 1) 0%)",
                  // default: res?.data?.top_bar_color ?? "#006450",
                },
              },
            },
          },
        },
      };

      applyUserTheme(newTheme);
    });
  }, []);

  useGlobalAuthHandler();
  return <Outlet />;
}

export default authenticationHoc(ProtectedLayout, {
  redirectTo: AppPaths.login,
});
