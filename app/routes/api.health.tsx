import type { LoaderFunctionArgs } from "@remix-run/node";

export async function loader({ request: _request }: LoaderFunctionArgs) {
  try {
    const healthStatus = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || "unknown",
      environment: process.env.NODE_ENV || "unknown",
      uptime: process.uptime(),
      checks: {
        server: {
          status: "up",
          message: "Server is running",
        },
      },
    };

    return new Response(JSON.stringify(healthStatus), {
      headers: {
        "Cache-Control": "no-cache, no-store, must-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      },
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Health check failed:", error);

    return new Response(
      JSON.stringify({
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || "unknown",
        environment: process.env.NODE_ENV || "unknown",
        uptime: process.uptime(),
        error: error instanceof Error ? error.message : "Unknown error",
        checks: {},
      }),
      {
        status: 503,
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      },
    );
  }
}
