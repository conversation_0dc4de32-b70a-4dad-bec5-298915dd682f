import { json } from "@remix-run/node";
import type { LoaderFunction } from "@remix-run/node";
import { useLoaderData, useRouteError, isRouteErrorResponse } from "@remix-run/react";

import { APIError } from "~/lib/errors/api.error";
import { Logger } from "~/lib/logger/logger";

const logger = Logger.getInstance();

export const loader: LoaderFunction = async () => {
  try {
    // Your data fetching logic here
    // For example:
    // const articles = await getArticles();
    return json({ message: "This is the Article page" });
  } catch (error) {
    logger.error("Article loader error", { error });
    throw new APIError({
      message: "Failed to load articles",
      code: "ARTICLE_LOAD_ERROR",
      status: 500,
    });
  }
};

export default function ArticlePage() {
  const data = useLoaderData<typeof loader>();
  return <h1>{data.message}</h1>;
}

// Route-level error boundary
export function ErrorBoundary() {
  const error = useRouteError();
  logger.error("Article route error boundary caught error", { error });

  if (isRouteErrorResponse(error)) {
    return (
      <div className="error-container">
        <h1>
          {error.status} {error.statusText}
        </h1>
        <p>{error.data}</p>
      </div>
    );
  }

  const errorMessage = error instanceof Error ? error.message : "Unknown error";

  return (
    <div className="error-container">
      <h1>Error loading articles</h1>
      <p>{errorMessage}</p>
      {process.env.NODE_ENV === "development" && error instanceof Error && <pre>{error.stack}</pre>}
    </div>
  );
}
