function combineURLs(baseURL: string, relativeURL: string) {
  return relativeURL
    ? `${baseURL.replace(/\/+$/, "")}/${relativeURL.replace(/^\/+/, "")}`
    : baseURL;
}

export function combineBaseUrl(prefix: string, path: string) {
  const match = path.match(/^\/([^/]+)(\/.*)?$/);
  if (!match) return path;

  const lang = match[1];
  const rest = match[2] || "";
  if (rest.includes(prefix)) return `/${lang}${prefix}`;
  return `/${lang}${prefix}${rest}`;
}

type RemovePrefixSlash<T extends string> = T extends `/${infer U}` ? RemovePrefixSlash<U> : T;

type RemoveSuffixSlash<T extends string> = T extends `${infer U}/` ? RemoveSuffixSlash<U> : T;

type PrependPath<Path extends string, Prefix extends string> = Prefix extends string
  ? `${Prefix}/${RemovePrefixSlash<RemoveSuffixSlash<Path>>}`
  : never;

export const prependPath = <Path extends string, Prefix extends string>(
  path: Path,
  prefix: Prefix,
) => {
  return combineURLs(prefix, path) as PrependPath<Path, Prefix>;
};

type Paths = Readonly<{
  [key: string]: string;
}>;

type PrependPaths<Pa extends Paths, Prefix extends string> = {
  [K in keyof Pa]: PrependPath<Pa[K], Prefix>;
};

type Combiner = (prefix: string, path: string) => string;

export const prependPathsUnified = <Pa extends Paths, Prefix extends string>(
  paths: Pa,
  prefix: Prefix,
  combiner: Combiner = combineURLs,
): PrependPaths<Pa, Prefix> => {
  const result = {} as any;
  const keys = Object.keys(paths);
  for (const key of keys) {
    result[key] = combiner(prefix, paths[key]);
  }
  return result;
};
