export const formatCurrency = (value: number | string, locale: string = 'en-US', currency: string = 'USD'): string => {
    const number = typeof value === 'string' ? parseFloat(value) : value;
  
    if (isNaN(number)) return '';
  
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(number);
  }
  