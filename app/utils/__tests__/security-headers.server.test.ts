import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";

import type { ServerEnvService } from "~/services/env";

import { getSecurityConfig, createSecurityHeaders } from "../security-headers.server";

// Create a mutable version of ServerEnvService for testing
type MutableServerEnvService = {
  -readonly [K in keyof ServerEnvService]: ServerEnvService[K];
};

// Mock the environment service
const mockEnvService = vi.hoisted(
  () =>
    ({
      // Common env variables
      MODE: "uat" as const,
      STRAPI_URL: "",
      STRAPI_TOKEN: "",
      APP_API_URL: "",

      // CSP variables
      CSP_UPGRADE_INSECURE_REQUESTS: "",
      CSP_DEFAULT_SRC: "",
      CSP_SCRIPT_SRC: "",
      CSP_BASE_URI: "",
      CSP_FRAME_ANCESTORS: "",
      CSP_FRAME_SRC: "",
      CSP_OBJECT_SRC: "",
      CSP_FORM_ACTION: "",
      CSP_IMG_SRC: "",
      CSP_STYLE_SRC: "",
      CSP_CONNECT_SRC: "",
      CSP_FONT_SRC: "",
      CSP_SCRIPT_SRC_ATTR: "",
      CSP_SCRIPT_SRC_ELEM: "",
      CSP_SCRIPT_SRC_NONCE: "",
      CSP_SCRIPT_SRC_NONCE_ATTR: "",
      CSP_SCRIPT_SRC_NONCE_ELEM: "",

      // HSTS variables
      HSTS_MAX_AGE: "",
      HSTS_INCLUDE_SUBDOMAINS: "",
      HSTS_PRELOAD: "",

      // Other security headers
      REFERRER_POLICY: "",
      CROSS_ORIGIN_RESOURCE_POLICY: "",
      X_CONTENT_TYPE_OPTIONS: "",
      X_DNS_PREFETCH_CONTROL: "",
      X_XSS_PROTECTION: "",
      X_FRAME_OPTIONS: "",
    }) as unknown as MutableServerEnvService,
);

// Mock the environment service module
vi.mock("~/services/env/env-impl.server", () => ({
  serverEnvServiceImpl: mockEnvService,
}));

describe("security-headers.server", () => {
  beforeEach(() => {
    // Reset all mock environment variables before each test
    Object.keys(mockEnvService).forEach((key) => {
      (mockEnvService as any)[key] = "";
    });
    // Set default MODE for tests
    mockEnvService.MODE = "uat";
  });

  describe("parseArrayEnv (internal function behavior)", () => {
    it("should return default value when env value is undefined", () => {
      const config = getSecurityConfig();
      expect(config.csp.defaultSrc).toEqual(["'self'"]);
    });

    it("should parse comma-separated values correctly", () => {
      mockEnvService.CSP_DEFAULT_SRC = "'self',https://example.com,data:";
      const config = getSecurityConfig();
      expect(config.csp.defaultSrc).toEqual(["'self'", "https://example.com", "data:"]);
    });

    it("should trim whitespace from values", () => {
      mockEnvService.CSP_SCRIPT_SRC = " 'self' , https://cdn.example.com , unsafe-inline ";
      const config = getSecurityConfig();
      expect(config.csp.scriptSrc).toEqual([
        "'self'",
        "https://cdn.example.com",
        "'unsafe-inline'",
      ]);
    });

    it("should filter out empty strings", () => {
      mockEnvService.CSP_IMG_SRC = "'self',,data:,";
      const config = getSecurityConfig();
      expect(config.csp.imgSrc).toEqual(["'self'", "data:"]);
    });

    it("should handle single value without comma", () => {
      mockEnvService.CSP_OBJECT_SRC = "'none'";
      const config = getSecurityConfig();
      expect(config.csp.objectSrc).toEqual(["'none'"]);
    });

    it("should automatically quote unquoted CSP keywords", () => {
      mockEnvService.CSP_STYLE_SRC = "self,unsafe-inline,https://fonts.googleapis.com";
      const config = getSecurityConfig();
      expect(config.csp.styleSrc).toEqual([
        "'self'",
        "'unsafe-inline'",
        "https://fonts.googleapis.com",
      ]);
    });

    it("should handle already quoted CSP keywords correctly", () => {
      mockEnvService.CSP_SCRIPT_SRC = "'self','strict-dynamic',https://cdn.example.com";
      const config = getSecurityConfig();
      expect(config.csp.scriptSrc).toEqual([
        "'self'",
        "'strict-dynamic'",
        "https://cdn.example.com",
      ]);
    });

    it("should fix malformed quotes on CSP keywords", () => {
      mockEnvService.CSP_DEFAULT_SRC = "self',none,'unsafe-inline";
      const config = getSecurityConfig();
      expect(config.csp.defaultSrc).toEqual(["'self'", "'none'", "'unsafe-inline'"]);
    });

    it("should handle mixed quoted and unquoted values", () => {
      mockEnvService.CSP_FORM_ACTION = "'self',none,https://payment.example.com";
      const config = getSecurityConfig();
      expect(config.csp.formAction).toEqual(["'self'", "'none'", "https://payment.example.com"]);
    });
  });

  describe("parseBooleanEnv", () => {
    it("should return default value when env value is undefined", () => {
      const config = getSecurityConfig();
      expect(config.hsts.includeSubDomains).toBe(true);
    });

    it('should parse "true" string to boolean true', () => {
      mockEnvService.HSTS_INCLUDE_SUBDOMAINS = "true";
      const config = getSecurityConfig();
      expect(config.hsts.includeSubDomains).toBe(true);
    });

    it('should parse "false" string to boolean false', () => {
      mockEnvService.HSTS_PRELOAD = "false";
      const config = getSecurityConfig();
      expect(config.hsts.preload).toBe(false);
    });

    it("should be case insensitive", () => {
      mockEnvService.CSP_UPGRADE_INSECURE_REQUESTS = "TRUE";
      const config = getSecurityConfig();
      expect(config.csp.upgradeInsecureRequests).toBe(true);
    });

    it('should return false for non-"true" values', () => {
      mockEnvService.HSTS_INCLUDE_SUBDOMAINS = "yes";
      const config = getSecurityConfig();
      expect(config.hsts.includeSubDomains).toBe(false);
    });
  });

  describe("parseNumberEnv", () => {
    it("should return default value when env value is undefined", () => {
      const config = getSecurityConfig();
      expect(config.hsts.maxAge).toBe(31536000);
    });

    it("should parse valid number string", () => {
      mockEnvService.HSTS_MAX_AGE = "86400";
      const config = getSecurityConfig();
      expect(config.hsts.maxAge).toBe(86400);
    });

    it("should return default value for invalid number string", () => {
      mockEnvService.HSTS_MAX_AGE = "invalid";
      const config = getSecurityConfig();
      expect(config.hsts.maxAge).toBe(31536000);
    });

    it("should parse zero correctly", () => {
      mockEnvService.HSTS_MAX_AGE = "0";
      const config = getSecurityConfig();
      expect(config.hsts.maxAge).toBe(0);
    });

    it("should parse negative numbers correctly", () => {
      mockEnvService.HSTS_MAX_AGE = "-100";
      const config = getSecurityConfig();
      expect(config.hsts.maxAge).toBe(-100);
    });
  });

  describe("getSecurityConfig", () => {
    describe("development environment", () => {
      beforeEach(() => {
        mockEnvService.MODE = "dev";
        // Since we can't easily mock import.meta.env in the security headers module,
        // we'll simulate the development behavior by setting the CSP_CONNECT_SRC
        mockEnvService.CSP_CONNECT_SRC = "'self',ws:,http://localhost:5173";
      });

      it("should include WebSocket connections in development", () => {
        const config = getSecurityConfig();
        expect(config.csp.connectSrc).toContain("ws:");
        // Note: The empty string "" gets filtered out by parseArrayEnv's filter(Boolean)
        // so we don't test for it here
      });

      it("should use default upgradeInsecureRequests value (true)", () => {
        const config = getSecurityConfig();
        expect(config.csp.upgradeInsecureRequests).toBe(true);
      });
    });

    describe("isDev logic with default CSP_CONNECT_SRC", () => {
      const originalNodeEnv = process.env.NODE_ENV;

      afterEach(() => {
        // Restore original NODE_ENV after each test
        process.env.NODE_ENV = originalNodeEnv;
      });

      it("should add WebSocket connections to default connect-src when NODE_ENV is development", () => {
        // Clear CSP_CONNECT_SRC to test default behavior
        mockEnvService.CSP_CONNECT_SRC = "";

        // Mock NODE_ENV to be "development"
        process.env.NODE_ENV = "development";

        const config = getSecurityConfig();

        // When CSP_CONNECT_SRC is empty and NODE_ENV is development,
        // the default should include WebSocket connections
        // Note: The empty string "" is included in the default array and doesn't get filtered
        // because parseArrayEnv is only called when envValue is not empty
        expect(config.csp.connectSrc).toContain("'self'");
        expect(config.csp.connectSrc).toContain("ws:");
        expect(config.csp.connectSrc).toContain(""); // Empty string is in the default array
        expect(config.csp.connectSrc).toHaveLength(3); // 'self', 'ws:', and ''
      });

      it("should not add WebSocket connections to default connect-src when NODE_ENV is not development", () => {
        // Clear CSP_CONNECT_SRC to test default behavior
        mockEnvService.CSP_CONNECT_SRC = "";

        // Mock NODE_ENV to be "production"
        process.env.NODE_ENV = "production";

        const config = getSecurityConfig();

        // When CSP_CONNECT_SRC is empty and NODE_ENV is not development,
        // the default should NOT include WebSocket connections
        expect(config.csp.connectSrc).toEqual(["'self'"]);
        expect(config.csp.connectSrc).not.toContain("ws:");
      });

      it("should not add WebSocket connections when NODE_ENV is test", () => {
        // Clear CSP_CONNECT_SRC to test default behavior
        mockEnvService.CSP_CONNECT_SRC = "";

        // Mock NODE_ENV to be "test"
        process.env.NODE_ENV = "test";

        const config = getSecurityConfig();

        // When CSP_CONNECT_SRC is empty and NODE_ENV is test,
        // the default should NOT include WebSocket connections
        expect(config.csp.connectSrc).toEqual(["'self'"]);
        expect(config.csp.connectSrc).not.toContain("ws:");
      });

      it("should respect custom CSP_CONNECT_SRC over default isDev behavior", () => {
        // Set custom CSP_CONNECT_SRC that doesn't include WebSocket
        mockEnvService.CSP_CONNECT_SRC = "'self',https://api.example.com";

        // Mock NODE_ENV to be "development"
        process.env.NODE_ENV = "development";

        const config = getSecurityConfig();

        // Custom CSP_CONNECT_SRC should override the default isDev behavior
        expect(config.csp.connectSrc).toEqual(["'self'", "https://api.example.com"]);
        expect(config.csp.connectSrc).not.toContain("ws:");
      });

      it("should add WebSocket connections when NODE_ENV is development and CSP_CONNECT_SRC includes custom values", () => {
        // Set custom CSP_CONNECT_SRC that includes some custom values
        mockEnvService.CSP_CONNECT_SRC =
          "'self',https://api.example.com,ws:,wss://websocket.example.com";

        // Mock NODE_ENV to be "development"
        process.env.NODE_ENV = "development";

        const config = getSecurityConfig();

        // Should parse the custom CSP_CONNECT_SRC values
        expect(config.csp.connectSrc).toEqual([
          "'self'",
          "https://api.example.com",
          "ws:",
          "wss://websocket.example.com",
        ]);
        expect(config.csp.connectSrc).toContain("ws:");
      });
    });

    describe("production environment", () => {
      beforeEach(() => {
        mockEnvService.MODE = "production";
      });

      it("should not include WebSocket connections in production", () => {
        const config = getSecurityConfig();
        expect(config.csp.connectSrc).not.toContain("ws:");
      });

      it("should use default upgradeInsecureRequests value (true)", () => {
        const config = getSecurityConfig();
        expect(config.csp.upgradeInsecureRequests).toBe(true);
      });
    });

    describe("other environment", () => {
      beforeEach(() => {
        mockEnvService.MODE = "uat";
      });

      it("should not include WebSocket connections in non-development environments", () => {
        const config = getSecurityConfig();
        expect(config.csp.connectSrc).not.toContain("ws:");
      });

      it("should use default upgradeInsecureRequests value (true)", () => {
        const config = getSecurityConfig();
        expect(config.csp.upgradeInsecureRequests).toBe(true);
      });
    });

    it("should use environment service variables when provided", () => {
      mockEnvService.CSP_DEFAULT_SRC = "'self',https://api.example.com";
      mockEnvService.CSP_SCRIPT_SRC = "'self',unsafe-inline";
      mockEnvService.HSTS_MAX_AGE = "86400";
      mockEnvService.REFERRER_POLICY = "strict-origin-when-cross-origin";

      const config = getSecurityConfig();

      expect(config.csp.defaultSrc).toEqual(["'self'", "https://api.example.com"]);
      expect(config.csp.scriptSrc).toEqual(["'self'", "'unsafe-inline'"]);
      expect(config.hsts.maxAge).toBe(86400);
      expect(config.referrerPolicy).toBe("strict-origin-when-cross-origin");
    });

    it("should return correct default CSP configuration", () => {
      const config = getSecurityConfig();

      expect(config.csp.defaultSrc).toEqual(["'self'"]);
      expect(config.csp.scriptSrc).toEqual(["'self'"]);
      expect(config.csp.baseSrc).toEqual(["'none'"]);
      expect(config.csp.frameAncestors).toEqual(["'none'"]);
      expect(config.csp.frameSrc).toEqual(["'none'"]);
      expect(config.csp.objectSrc).toEqual(["'none'"]);
      expect(config.csp.formAction).toEqual(["'self'"]);
      expect(config.csp.imgSrc).toEqual(["'self'", "data:", "https:"]);
      expect(config.csp.styleSrc).toEqual(["'self'", "'unsafe-inline'"]);
    });

    it("should return correct default HSTS configuration", () => {
      const config = getSecurityConfig();

      expect(config.hsts.maxAge).toBe(31536000);
      expect(config.hsts.includeSubDomains).toBe(true);
      expect(config.hsts.preload).toBe(true);
    });

    it("should return correct default security headers", () => {
      const config = getSecurityConfig();

      expect(config.referrerPolicy).toBe("origin-when-cross-origin");
      expect(config.crossOriginResourcePolicy).toBe("same-origin");
      expect(config.xContentTypeOptions).toBe("nosniff");
      expect(config.xDnsPrefetchControl).toBe("on");
      expect(config.xXssProtection).toBe("1; mode=block");
      expect(config.xFrameOptions).toBe("DENY");
    });

    it("should handle CSP upgrade insecure requests override", () => {
      mockEnvService.CSP_UPGRADE_INSECURE_REQUESTS = "false";
      const config = getSecurityConfig();
      expect(config.csp.upgradeInsecureRequests).toBe(false);
    });
  });

  describe("createSecurityHeaders", () => {
    const mockNonce = "test-nonce-123";

    it("should create headers with nonce in script-src", () => {
      const config = getSecurityConfig();
      const headers = createSecurityHeaders(mockNonce, config);

      expect(headers["Content-Security-Policy"]!["script-src"]).toContain(`'nonce-${mockNonce}'`);
      expect(headers["Content-Security-Policy"]!["script-src"]).toContain("'strict-dynamic'");
    });

    it("should merge custom script sources with nonce and strict-dynamic", () => {
      mockEnvService.CSP_SCRIPT_SRC = "'self',unsafe-eval";
      const config = getSecurityConfig();
      const headers = createSecurityHeaders(mockNonce, config);

      expect(headers["Content-Security-Policy"]!["script-src"]).toEqual([
        "'self'",
        "'unsafe-eval'",
        `'nonce-${mockNonce}'`,
        "'strict-dynamic'",
      ]);
    });

    it("should use CSP configuration from SecurityConfig", () => {
      mockEnvService.CSP_DEFAULT_SRC = "'self',https://api.example.com";
      mockEnvService.CSP_IMG_SRC = "'self',data:,https://cdn.example.com";

      const config = getSecurityConfig();
      const headers = createSecurityHeaders(mockNonce, config);
      const csp = headers["Content-Security-Policy"]!;

      expect(csp["default-src"]).toEqual(["'self'", "https://api.example.com"]);
      expect(csp["img-src"]).toEqual(["'self'", "data:", "https://cdn.example.com"]);
      expect(csp["upgrade-insecure-requests"]).toBe(config.csp.upgradeInsecureRequests);
    });

    it("should set HSTS values from configuration", () => {
      mockEnvService.HSTS_MAX_AGE = "86400";
      mockEnvService.HSTS_INCLUDE_SUBDOMAINS = "false";
      mockEnvService.HSTS_PRELOAD = "false";

      const config = getSecurityConfig();
      const headers = createSecurityHeaders(mockNonce, config);

      expect(headers["Strict-Transport-Security"]).toEqual({
        maxAge: 86400,
        includeSubDomains: false,
        preload: false,
      });
    });

    it("should set hardcoded security header values", () => {
      const config = getSecurityConfig();
      const headers = createSecurityHeaders(mockNonce, config);

      expect(headers["Referrer-Policy"]).toBe("origin-when-cross-origin");
      expect(headers["Cross-Origin-Resource-Policy"]).toBe("same-origin");
      expect(headers["X-Content-Type-Options"]).toBe("nosniff");
      expect(headers["X-DNS-Prefetch-Control"]).toBe("on");
      expect(headers["X-XSS-Protection"]).toBe("1; mode=block");
      expect(headers["X-Frame-Options"]).toBe("DENY");
    });

    it("should create complete CSP object", () => {
      const config = getSecurityConfig();
      const headers = createSecurityHeaders(mockNonce, config);
      const csp = headers["Content-Security-Policy"]!;

      expect(csp).toHaveProperty("upgrade-insecure-requests");
      expect(csp).toHaveProperty("default-src");
      expect(csp).toHaveProperty("script-src");
      expect(csp).toHaveProperty("base-uri");
      expect(csp).toHaveProperty("frame-ancestors");
      expect(csp).toHaveProperty("frame-src");
      expect(csp).toHaveProperty("object-src");
      expect(csp).toHaveProperty("form-action");
      expect(csp).toHaveProperty("img-src");
      expect(csp).toHaveProperty("style-src");
      expect(csp).toHaveProperty("connect-src");
    });

    it("should handle empty nonce gracefully", () => {
      const config = getSecurityConfig();
      const headers = createSecurityHeaders("", config);

      expect(headers["Content-Security-Policy"]!["script-src"]).toContain("'nonce-'");
    });

    it("should handle special characters in nonce", () => {
      const specialNonce = "nOnCe-123!@#$%^&*()";
      const config = getSecurityConfig();
      const headers = createSecurityHeaders(specialNonce, config);

      expect(headers["Content-Security-Policy"]!["script-src"]).toContain(
        `'nonce-${specialNonce}'`,
      );
    });

    it("should handle properly quoted CSP keywords from environment", () => {
      mockEnvService.CSP_STYLE_SRC = "'self','unsafe-inline',https://fonts.googleapis.com";
      const config = getSecurityConfig();
      const headers = createSecurityHeaders(mockNonce, config);

      expect(headers["Content-Security-Policy"]!["style-src"]).toEqual([
        "'self'",
        "'unsafe-inline'",
        "https://fonts.googleapis.com",
      ]);
    });
  });

  describe("quote handling edge cases", () => {
    it("should handle malformed quotes on CSP keywords", () => {
      mockEnvService.CSP_DEFAULT_SRC = "self',none,'unsafe-inline";
      const config = getSecurityConfig();
      expect(config.csp.defaultSrc).toEqual(["'self'", "'none'", "'unsafe-inline'"]);
    });

    it("should handle mixed quote styles", () => {
      mockEnvService.CSP_SCRIPT_SRC = `"self",'unsafe-eval',strict-dynamic`;
      const config = getSecurityConfig();
      expect(config.csp.scriptSrc).toEqual([`"self"`, "'unsafe-eval'", "'strict-dynamic'"]);
    });

    it("should preserve non-CSP-keyword quotes", () => {
      mockEnvService.CSP_CONNECT_SRC = "'self','wss://example.com','https://api.example.com'";
      const config = getSecurityConfig();
      expect(config.csp.connectSrc).toEqual([
        "'self'",
        "'wss://example.com'",
        "'https://api.example.com'",
      ]);
    });
  });

  describe("integration tests", () => {
    it("should work with complete environment configuration", () => {
      // Set up a complete environment
      mockEnvService.MODE = "uat";
      mockEnvService.CSP_UPGRADE_INSECURE_REQUESTS = "true";
      mockEnvService.CSP_DEFAULT_SRC = "'self'";
      mockEnvService.CSP_SCRIPT_SRC = "'self',strict-dynamic";
      mockEnvService.CSP_CONNECT_SRC = "'self',https://api.example.com";
      mockEnvService.HSTS_MAX_AGE = "31536000";
      mockEnvService.HSTS_INCLUDE_SUBDOMAINS = "true";
      mockEnvService.REFERRER_POLICY = "strict-origin-when-cross-origin";

      const config = getSecurityConfig();
      const headers = createSecurityHeaders("test-nonce", config);

      expect(config.csp.upgradeInsecureRequests).toBe(true);
      expect(config.csp.connectSrc).toEqual(["'self'", "https://api.example.com"]);
      expect(config.csp.scriptSrc).toEqual(["'self'", "'strict-dynamic'"]);
      expect(headers["Content-Security-Policy"]!["script-src"]).toContain("'nonce-test-nonce'");
      expect(headers["Referrer-Policy"]).toBe("origin-when-cross-origin"); // Note: hardcoded in function
    });

    it("should maintain type safety with CreateSecureHeaders", () => {
      const config = getSecurityConfig();
      const headers = createSecurityHeaders("test", config);

      // TypeScript should ensure these properties exist and have correct types
      expect(typeof headers["Content-Security-Policy"]).toBe("object");
      expect(typeof headers["Strict-Transport-Security"]).toBe("object");
      expect(typeof headers["Referrer-Policy"]).toBe("string");
    });
  });
});
