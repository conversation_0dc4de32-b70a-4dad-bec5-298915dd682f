import { redirect, type LoaderFunctionArgs } from "@remix-run/node";
import { pick } from "accept-language-parser";

import { DEFAULT_LANGUAGE, SUPPORTED_LANGUAGES, type SupportedLanguage } from "~/i18n/i18n-options";
// import { AppPaths, CONSTANTS } from '~/path/to/your/constants'; // TODO: Update this import path

/**
 * Handles the initial language-based redirect for the root loader.
 */
export const langRedirectHandler = ({ params, request }: Omit<LoaderFunctionArgs, "context">) => {
  const { pathname, search } = new URL(request.url);

  /**
   * Determines if the user should be redirected based on the URL locale.
   */
  const shouldRedirect = (): boolean => {
    return !params.lang || !(SUPPORTED_LANGUAGES as readonly string[]).includes(params.lang);
  };

  /**
   * Gets the preferred language from the 'accept-language' header.
   * Falls back to the default locale if no supported language is found.
   */
  const getPreferredLanguage = (): SupportedLanguage => {
    const acceptLanguageHeader = request.headers.get("accept-language");
    if (!acceptLanguageHeader) {
      return DEFAULT_LANGUAGE;
    }

    const preferredLang = pick(SUPPORTED_LANGUAGES as readonly string[], acceptLanguageHeader, {
      loose: true,
    });

    return (preferredLang as SupportedLanguage) || DEFAULT_LANGUAGE; // Fallback
  };

  /**
   * Builds the final redirect URI.
   */
  const getRedirectUri = (): string => {
    const preferredLang = getPreferredLanguage();
    const baseUri = import.meta.env.VITE_APP_BASE_URI;
    if (pathname.startsWith(baseUri)) {
      return `/${preferredLang}${pathname}${search}`;
    }

    // Prepend the language and preserve the original path and search params.
    return `/${preferredLang}${baseUri}${pathname}${search}`;
  };

  return {
    getRedirectUri,
    performRedirect: redirect,
    shouldRedirect,
  };
};
