import { i18nOptions, SupportedLanguage, DEFAULT_LANGUAGE } from "../i18n/i18n-options";

export const getLangFromUrl = (url: string, fallback = DEFAULT_LANGUAGE) => {
  try {
    const { pathname } = new URL(url);
    const lang = pathname.split("/")[1];
    if (i18nOptions.supportedLngs.includes(lang as SupportedLanguage)) {
      return lang;
    }

    return fallback;
  } catch (error) {
    console.error(error, "Error getting lang from url");
    return fallback;
  }
};
