import { CreateSecureHeaders } from "@mcansh/http-helmet";

import { serverEnvServiceImpl as envService } from "~/services/env/env-impl.server";

export interface SecurityConfig {
  csp: {
    upgradeInsecureRequests: boolean;
    defaultSrc: string[];
    scriptSrc: string[];
    baseSrc: string[];
    frameAncestors: string[];
    frameSrc: string[];
    objectSrc: string[];
    formAction: string[];
    imgSrc: string[];
    styleSrc: string[];
    connectSrc: string[];
  };
  hsts: {
    maxAge: number;
    includeSubDomains: boolean;
    preload: boolean;
  };
  referrerPolicy: string;
  crossOriginResourcePolicy: string;
  xContentTypeOptions: string;
  xDnsPrefetchControl: string;
  xXssProtection: string;
  xFrameOptions: string;
}

function parseArrayEnv(envValue: string | undefined, defaultValue: string[]): string[] {
  if (!envValue) return defaultValue;
  return envValue
    .split(",")
    .map((item) => {
      const trimmed = item.trim();
      const unquoted = trimmed.replace(/^'|'$/g, "");
      // Ensure CSP keywords are properly quoted
      if (["self", "none", "unsafe-inline", "unsafe-eval", "strict-dynamic"].includes(unquoted)) {
        return `'${unquoted}'`;
      }
      return trimmed;
    })
    .filter(Boolean);
}

function parseBooleanEnv(envValue: string | undefined, defaultValue: boolean): boolean {
  if (!envValue) return defaultValue;
  return envValue.toLowerCase() === "true";
}

function parseNumberEnv(envValue: string | undefined, defaultValue: number): number {
  if (!envValue) return defaultValue;
  const parsed = parseInt(envValue, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

export function getSecurityConfig(): SecurityConfig {
  const isDev = process.env.NODE_ENV === "development";

  return {
    csp: {
      upgradeInsecureRequests: parseBooleanEnv(envService.CSP_UPGRADE_INSECURE_REQUESTS, true),
      defaultSrc: parseArrayEnv(envService.CSP_DEFAULT_SRC, ["'self'"]),
      scriptSrc: parseArrayEnv(envService.CSP_SCRIPT_SRC, ["'self'"]),
      baseSrc: parseArrayEnv(envService.CSP_BASE_URI, ["'none'"]),
      frameAncestors: parseArrayEnv(envService.CSP_FRAME_ANCESTORS, ["'none'"]),
      frameSrc: parseArrayEnv(envService.CSP_FRAME_SRC, ["'none'"]),
      objectSrc: parseArrayEnv(envService.CSP_OBJECT_SRC, ["'none'"]),
      formAction: parseArrayEnv(envService.CSP_FORM_ACTION, ["'self'"]),
      imgSrc: parseArrayEnv(envService.CSP_IMG_SRC, ["'self'", "data:", "https:"]),
      styleSrc: parseArrayEnv(envService.CSP_STYLE_SRC, ["'self'", "'unsafe-inline'"]),
      connectSrc: parseArrayEnv(envService.CSP_CONNECT_SRC, [
        "'self'",
        ...(isDev ? ["ws:", ""] : []),
      ]),
    },
    hsts: {
      maxAge: parseNumberEnv(envService.HSTS_MAX_AGE, 31536000),
      includeSubDomains: parseBooleanEnv(envService.HSTS_INCLUDE_SUBDOMAINS, true),
      preload: parseBooleanEnv(envService.HSTS_PRELOAD, true),
    },
    referrerPolicy: envService.REFERRER_POLICY || "origin-when-cross-origin",
    crossOriginResourcePolicy: envService.CROSS_ORIGIN_RESOURCE_POLICY || "same-origin",
    xContentTypeOptions: envService.X_CONTENT_TYPE_OPTIONS || "nosniff",
    xDnsPrefetchControl: envService.X_DNS_PREFETCH_CONTROL || "on",
    xXssProtection: envService.X_XSS_PROTECTION || "1; mode=block",
    xFrameOptions: envService.X_FRAME_OPTIONS || "DENY",
  };
}

export function createSecurityHeaders(nonce: string, config: SecurityConfig): CreateSecureHeaders {
  return {
    "Content-Security-Policy": {
      "upgrade-insecure-requests": config.csp.upgradeInsecureRequests,
      "default-src": config.csp.defaultSrc,
      "script-src": [...config.csp.scriptSrc, `'nonce-${nonce}'`, "'strict-dynamic'"],
      "base-uri": config.csp.baseSrc,
      "frame-ancestors": config.csp.frameAncestors,
      "frame-src": config.csp.frameSrc,
      "object-src": config.csp.objectSrc,
      "form-action": config.csp.formAction,
      "img-src": config.csp.imgSrc,
      "style-src": config.csp.styleSrc,
      "connect-src": config.csp.connectSrc,
    },
    "Strict-Transport-Security": {
      maxAge: config.hsts.maxAge,
      includeSubDomains: config.hsts.includeSubDomains,
      preload: config.hsts.preload,
    },
    "Referrer-Policy": "origin-when-cross-origin",
    "Cross-Origin-Resource-Policy": "same-origin",
    "X-Content-Type-Options": "nosniff",
    "X-DNS-Prefetch-Control": "on",
    "X-XSS-Protection": "1; mode=block",
    "X-Frame-Options": "DENY",
  };
}
