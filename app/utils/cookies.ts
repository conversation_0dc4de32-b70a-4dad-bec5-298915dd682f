import { createCookie } from "@remix-run/node";

export const authCookie = createCookie("auth", {
  httpOnly: true,
  secure: process.env.NODE_ENV === "production",
  sameSite: "lax",
  maxAge: 7 * 24 * 60 * 60,
  path: "/",
});

export const getAuthToken = async (request: Request) => {
  return await authCookie.parse(request.headers.get("Cookie"));
};

export const setAuthToken = async (token: string) => {
  return await authCookie.serialize(token);
};
