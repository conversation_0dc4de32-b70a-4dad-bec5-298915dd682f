import { css, useTheme } from "@emotion/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "@remix-run/react";
import {
  AppTheme,
  RDSButton,
  RDSTypography,
  RDSSwitch,
  RDSAssetWrapper,
  RDSProgressSteps,
} from "@roshn/ui-kit";
import { useState } from "react";
import { useForm, Controller } from "react-hook-form";
import { z, ZodTypeAny } from "zod";
import isEmpty from "lodash/isEmpty";

import { Input } from "~/components/form-components/input/input";
import { Select } from "~/components/form-components/select/select";
import { DatePicker } from "~/components/form-components/date-picker/date-picker";
import { TagSelector } from "~/components/form-components/tag-selector/tags-selector";
import { ButtonFileUpload } from "~/components/form-components/file-upload/button-file-upload";
import { TextArea } from "~/components/form-components/text-area/text-area";
import { useAppPath } from "~/hooks/use-app-path";
import { useInjection } from "~/hooks/use-di";
import { ProductService } from "~/services/product-list/product-list";
import { AppPaths } from "~/utils/app-paths";
import { InputNumber } from "~/components/form-components/input-number/input-number";
import { createSvg } from "~/components/svgs";
import { Section } from "~/components/sections/sections";

const formSchema = {
  id: 1,
  name: "Real Estate - Off-plan",
  name_en: "Real Estate - Off-plan",
  name_ar: "وحدات عقارية على الخارطة",
  slug: "real-estate-off-plan",
  template_id: "real_estate_off_plan_v1",
  version: 1,
  description: "Real Estate - Off-plan Projects",
  description_en: "Real Estate - Off-plan Projects",
  description_ar: "Real Estate - Off-plan Projects",
  is_active: true,
  order: 0,
  marketplace: 1,
  marketplace_merchant: null,
  product_attributes: [
    {
      id: 14,
      name: "unitCode",
      slug: "unitcode",
      attribute_type: "TEXT",
      options: [],
      is_required: true,
      is_translatable: false,
      order: 0,
    },
    {
      id: 18,
      name: "RegaLicenceNumber",
      slug: "regalicencenumber",
      attribute_type: "TEXT",
      options: [],
      is_required: true,
      is_translatable: false,
      order: 0,
    },
    {
      id: 15,
      name: "propertyType",
      slug: "propertytype",
      attribute_type: "SELECT",
      options: ["Townhouse", "Villa", "Apartment"],
      is_required: true,
      is_translatable: false,
      order: 1,
    },
    {
      id: 16,
      name: "numberofBedrooms",
      slug: "numberofbedrooms",
      attribute_type: "COUNTER",
      options: [],
      is_required: true,
      is_translatable: false,
      order: 2,
    },
    {
      id: 20,
      name: "grossFloorAreaSqm",
      slug: "grossfloorareasqm",
      attribute_type: "NUMBER",
      options: [],
      is_required: false,
      is_translatable: false,
      order: 3,
    },
    {
      id: 17,
      name: "numberofBathrooms",
      slug: "numberofbathrooms",
      attribute_type: "COUNTER",
      options: [],
      is_required: true,
      is_translatable: false,
      order: 4,
    },
  ],
  category_attributes: [
    {
      id: 1,
      name: "projectName",
      slug: "projectname",
      attribute_type: "TEXT",
      options: [],
      is_required: true,
      is_translatable: true,
      order: 0,
      value: null,
    },
    {
      id: 2,
      name: "logo",
      slug: "logo",
      attribute_type: "IMAGE",
      options: [],
      is_required: true,
      is_translatable: true,
      order: 1,
      value: [],
    },
    {
      id: 3,
      name: "address",
      slug: "address",
      attribute_type: "TEXT",
      options: [],
      is_required: true,
      is_translatable: true,
      order: 2,
      value: null,
    },
    {
      id: 4,
      name: "city",
      slug: "city",
      attribute_type: "TEXT",
      options: [],
      is_required: true,
      is_translatable: true,
      order: 3,
      value: null,
    },
    {
      id: 5,
      name: "handoverDate",
      slug: "handoverdate",
      attribute_type: "DATE",
      options: [],
      is_required: true,
      is_translatable: true,
      order: 4,
      value: null,
    },
    {
      id: 6,
      name: "description",
      slug: "description",
      attribute_type: "TEXT",
      options: [],
      is_required: true,
      is_translatable: true,
      order: 5,
      value: null,
    },
    {
      id: 7,
      name: "nearbyAmenities",
      slug: "nearbyamenities",
      attribute_type: "MULTI_SELECT",
      options: [
        "Dining & Entertainmen",
        "Health Centre",
        "Kindergarten",
        "Mosque",
        "Public Park",
        "Retail Centres",
        "School",
        "Sports Ground",
      ],
      is_required: true,
      is_translatable: true,
      order: 6,
      value: null,
    },
    {
      id: 8,
      name: "paymentPlanReservationFeeRefundable",
      slug: "paymentplanreservationfeerefundable",
      attribute_type: "BOOLEAN",
      options: [],
      is_required: true,
      is_translatable: false,
      order: 7,
      value: null,
    },
    {
      id: 9,
      name: "paymentPlanFeeType",
      slug: "paymentplanfeetype",
      attribute_type: "SELECT",
      options: ["Fixed", "Percentage of Total"],
      is_required: true,
      is_translatable: false,
      order: 8,
      value: null,
    },
    {
      id: 10,
      name: "reservationFeeAmount",
      slug: "reservationfeeamount",
      attribute_type: "NUMBER",
      options: [],
      is_required: true,
      is_translatable: false,
      order: 9,
      value: null,
    },
    {
      id: 11,
      name: "paymentPlanDownPayment",
      slug: "paymentplandownpayment",
      attribute_type: "NUMBER",
      options: [],
      is_required: true,
      is_translatable: false,
      order: 10,
      value: null,
    },
    {
      id: 12,
      name: "paymentPlanHandover",
      slug: "paymentplanhandover",
      attribute_type: "NUMBER",
      options: [],
      is_required: true,
      is_translatable: false,
      order: 11,
      value: null,
    },
    {
      id: 13,
      name: "paymentPlanDuringConstruction",
      slug: "paymentplanduringconstruction",
      attribute_type: "NUMBER",
      options: [],
      is_required: true,
      is_translatable: false,
      order: 12,
      value: null,
    },
    {
      id: 19,
      name: "RegaLicenceNumber",
      slug: "regalicencenumber",
      attribute_type: "NUMBER",
      options: [],
      is_required: true,
      is_translatable: false,
      order: 13,
      value: null,
    },
  ],
  attributes_count: 20,
  product_attributes_count: 6,
  category_attributes_count: 14,
  categories_using_template: [],
  created_date: "2025-07-16T11:10:30.731498Z",
  updated_date: "2025-07-16T11:14:21.906338Z",
  marketplace_asset_categories: [
    {
      id: 1,
      name: "Brochures",
      slug: "brochures",
      description: "Brochure for the project",
      allowed_file_types: ["pdf", "jpg", "png"],
      max_file_size: 5,
      order: 0,
      is_active: true,
      assets_count: 0,
    },
    {
      id: 2,
      name: "Floor Plans",
      slug: "floor-plans",
      description: "Floor Plans",
      allowed_file_types: ["pdf", "jpg", "png"],
      max_file_size: 5,
      order: 0,
      is_active: true,
      assets_count: 0,
    },
    {
      id: 3,
      name: "Masterplan",
      slug: "masterplan",
      description: "masterplan",
      allowed_file_types: ["pdf", "jpg", "png"],
      max_file_size: 5,
      order: 0,
      is_active: true,
      assets_count: 0,
    },
  ],
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),

    form: css({
      flex: "0 0 70%",
    }),

    sectionLayout: (theme: AppTheme) =>
      css({
        display: "flex",
        flexDirection: "column",
        gap: theme.rds.dimension["400"],
      }),  

  sectionWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      backgroundColor: theme.rds.color.background.ui.canvas,
      padding: theme.rds.dimension["300"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  sectionHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
      textTransform: "uppercase",
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  infoHead: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.ui.tertiary,
    }),

  infoDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.emphasis.lg,
      color: theme.rds.color.text.ui.primary,
    }),

  internalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  hideShowUnitWrapper: () =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
    }),  

  sectionChildrenWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  sectionDivider: (theme: AppTheme) =>
    css({
      height: "1px",
      backgroundColor: theme.rds.color.border.ui.primary,
    }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      height: "fit-content",
      width: "100%",
    }),

  infoTypoWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  infoSections: (theme: AppTheme) => css({
    display: "flex",
    flexDirection: "column",
    flex: "0 0 30%",
    gap: theme?.rds?.dimension[200]
  }),

  actionsLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),

  listingTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      color: theme.rds.color.text.ui.tertiary,
    }),

  withLabelFieldWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      gap: theme?.rds?.dimension[100],
    }),
  fieldLabel: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.md,
      color: theme.rds.color.text.ui.primary,
      display: "flex",
      alignItems: "center",
      gap: theme?.rds?.dimension[50],
    }),
};

// const Check = createSvg(() => import("~/assets/icons/check.svg"));

type StepState = "active" | "complete" | "in-complete";

type StepId = "draft" | "in-review" | "approved";

type Step = {
  stepTitle: string;
  state: StepState;
  id: StepId;
};

const baseTypeMap: Record<string, (attr: any) => ZodTypeAny> = {
  TEXT: () => z.string().min(1, "This field is required"),
  TEXTAREA: () => z.string().min(1, "This field is required"),
  NUMBER: () => z.string(),
  COUNTER: () => z.number().min(0, "Value must be 0 or greater"),
  MULTI_SELECT: () => z.array(z.string().min(1, "This field is required")),
  DATE: () => z.string().min(1, "This field is required"),
  BOOLEAN: () => z.boolean(),
  FILE: () => z.any(),
  SELECT: (attr) => {
    if (attr.options && attr.options.length > 0) {
      const optionValues = attr.options.map((opt: any) => opt.value || opt);
      return z.enum([...new Set(optionValues)] as [string, ...string[]]);
    }
    return z.string().min(1, "This field is required");
  },
};

const createZodFieldSchema = (attr: any): ZodTypeAny => {
  const generator = baseTypeMap[attr.attribute_type] ?? (() => z.any());
  let schema = generator(attr);

  if (!attr.is_required) {
    schema = schema.optional();
  }

  return schema;
};

const createFormSchema = (formSchemaData: any) => {
  const schemaFields: Record<string, ZodTypeAny> = {};

  console.log('🔧 CREATING FORM SCHEMA:');

  // Add product attributes to schema
  formSchemaData.product_attributes.forEach((attr: any) => {
    schemaFields[attr.slug] = createZodFieldSchema(attr);
    console.log(`  📝 Product Attribute: ${attr.slug} (${attr.attribute_type}) - Required: ${attr.is_required}`);
  });

  // Add category attributes to schema
  formSchemaData.category_attributes.forEach((attr: any) => {
    schemaFields[attr.slug] = createZodFieldSchema(attr);
    console.log(`  📝 Category Attribute: ${attr.slug} (${attr.attribute_type}) - Required: ${attr.is_required}`);
  });

  // Add asset categories to schema (for file uploads)
  formSchemaData.marketplace_asset_categories.forEach((assetCategory: any) => {
    schemaFields[assetCategory.slug] = z.any().optional(); // File uploads are typically optional
    console.log(`  📎 Asset Category: ${assetCategory.slug} - Optional`);
  });

  console.log('🔧 Schema fields created:', Object.keys(schemaFields));

  return z.object(schemaFields);
};

export default function AddProductPage({
  defaultValues = undefined,
}: {
  defaultValues?: Record<string, any>;
}) {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();
  const productService = useInjection<ProductService>(ProductService);
  const [loading, setLoading] = useState(false);
  const theme = useTheme() as AppTheme;

  const dynamicSchema = createFormSchema(formSchema);

  // Create default values for all form fields to ensure proper validation
  const createDefaultValues = () => {
    const defaults: Record<string, any> = {};

    // Set defaults for product attributes
    formSchema.product_attributes.forEach((attr: any) => {
      if (attr.attribute_type === 'COUNTER') {
        defaults[attr.slug] = 0; // Number 0 for COUNTER
      } else if (attr.attribute_type === 'NUMBER') {
        defaults[attr.slug] = ''; // Empty string for NUMBER
      } else if (attr.attribute_type === 'BOOLEAN') {
        defaults[attr.slug] = false;
      } else if (attr.attribute_type === 'MULTI_SELECT') {
        defaults[attr.slug] = [];
      } else if (attr.attribute_type === 'IMAGE') {
        defaults[attr.slug] = [];
      } else {
        defaults[attr.slug] = '';
      }
    });

    // Set defaults for category attributes
    formSchema.category_attributes.forEach((attr: any) => {
      if (attr.attribute_type === 'COUNTER') {
        defaults[attr.slug] = 0; // Number 0 for COUNTER
      } else if (attr.attribute_type === 'NUMBER') {
        defaults[attr.slug] = ''; // Empty string for NUMBER
      } else if (attr.attribute_type === 'BOOLEAN') {
        defaults[attr.slug] = false;
      } else if (attr.attribute_type === 'MULTI_SELECT') {
        defaults[attr.slug] = [];
      } else if (attr.attribute_type === 'IMAGE') {
        defaults[attr.slug] = [];
      } else {
        defaults[attr.slug] = '';
      }
    });

    // Set defaults for asset categories
    formSchema.marketplace_asset_categories.forEach((assetCategory: any) => {
      defaults[assetCategory.slug] = [];
    });

    return { ...defaults, ...defaultValues };
  };

  const {
    handleSubmit,
    formState: { isValid, errors, touchedFields },
    control,
    watch,
  } = useForm({
    resolver: zodResolver(dynamicSchema),
    mode: "onChange",
    defaultValues: createDefaultValues(),
  });

  // Watch COUNTER fields to debug reset issues
  const counterValues = watch(['numberofbedrooms', 'numberofbathrooms']);
  console.log('🔢 COUNTER VALUES:', {
    numberofbedrooms: counterValues[0],
    numberofbathrooms: counterValues[1]
  });

  const [steps, setSteps] = useState<Step[]>([
    { stepTitle: "Draft", state: "active", id: "draft" },
    { stepTitle: "In review", state: "in-complete", id: "in-review" },
    { stepTitle: "Approved", state: "in-complete", id: "approved" },
  ]);

  const handleInventoryNav = () => {
    navigate(generateAppPath(AppPaths.inventory));
  };

  const handleDiscard = () => {
    navigate(generateAppPath(AppPaths.inventory));
  };

  const toHumanReadable = (label: string) => {
    return label.replace(/([a-z])([A-Z])/g, "$1 $2").replace(/^./, (str) => str.toUpperCase());
  };

  const fieldsError = !isEmpty(errors);
  const fieldsTouched = !isEmpty(touchedFields);

  let statusMessage = "";

  if (fieldsError) {
    statusMessage = "Fix the errors marked in the form before saving.";
  } else if (fieldsTouched) {
    statusMessage = "You have unsaved changes.";
  } else {
    statusMessage = "No changes to be saved.";
  }

  // Enhanced debug logging to understand form validation state
  console.log('=== FORM VALIDATION DEBUG ===');
  console.log('isValid:', isValid);
  console.log('fieldsError:', fieldsError);
  console.log('fieldsTouched:', fieldsTouched);

  if (!isEmpty(errors)) {
    console.log('🔴 ERRORS DETECTED:');
    Object.entries(errors).forEach(([fieldName, error]) => {
      console.log(`  - Field "${fieldName}":`, error);
    });
  } else {
    console.log('✅ No errors detected');
  }

  if (!isEmpty(touchedFields)) {
    console.log('👆 TOUCHED FIELDS:');
    Object.entries(touchedFields).forEach(([fieldName, touched]) => {
      console.log(`  - Field "${fieldName}": ${touched ? 'touched' : 'not touched'}`);
    });
  } else {
    console.log('👆 No fields touched yet');
  }

  console.log('Status Message:', statusMessage);
  console.log('===============================');


  const RenderFieldWithLabel = ({
    name,
    children,
  }: {
    name: string;
    children: React.ReactNode;
  }) => (
    <div css={styles.withLabelFieldWrapper(theme)}>
      <RDSTypography
        css={styles.fieldLabel(theme)}
        fontName={theme?.rds?.typographies?.label.md}
        color={theme?.rds?.color?.text?.ui?.primary}
      >
        {toHumanReadable(name)}
        {<span css={{ color: theme?.rds?.color?.text?.functional?.danger?.tertiary }}>*</span>}
      </RDSTypography>
      {children}
    </div>
  );

  const renderFormField = (attribute: any) => {
    const { id, name, slug, attribute_type, options, is_required } = attribute;
    const key = `${slug}-${id}`;

    switch (attribute_type) {
      case "TEXT":
        const isDescriptionField = name.toLowerCase().includes("description");

        return isDescriptionField ? (
          <TextArea
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            placeholder={`Enter ${name.toLowerCase()}...`}
            rows={4}
          />
        ) : (
          <Input
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            isRequired={is_required}
            placeholder={`Enter ${toHumanReadable(name).toLowerCase()}...`}
          />
        );

      case "NUMBER":
        return (
          <Input
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            type="number"
            isRequired={is_required}
            placeholder={`Enter ${toHumanReadable(name).toLowerCase()}...`}
          />
        );

      case "COUNTER":
        return (
          <RenderFieldWithLabel name={name}>
            <InputNumber
              key={key}
              name={slug}
              control={control}
              label={toHumanReadable(name)}
              // type="number"
              // isRequired={is_required}
              // placeholder={`Enter ${name.toLowerCase()}...`}
            />
          </RenderFieldWithLabel>
        );

      case "SELECT":
        return (
          <Select
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            isRequired={is_required}
            placeholder={`Select ${toHumanReadable(name).toLowerCase()}...`}
            options={options.map((option: string) => ({
              label: option,
              value: option,
            }))}
          />
        );

      case "MULTI_SELECT":
        return (
          <TagSelector
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            description={`Select multiple ${toHumanReadable(name).toLowerCase()}`}
            options={options.map((option: string) => ({
              label: option,
              value: option.toLowerCase().replace(/\s+/g, "-"),
            }))}
          />
        );

      case "BOOLEAN":
        return (
          <RenderFieldWithLabel name={name}>
            <Controller
              key={key}
              name={slug}
              control={control}
              render={({ field: { value, onChange } }) => (
                <RDSSwitch checked={value || false} onChange={(checked) => onChange(checked)} />
              )}
            />
          </RenderFieldWithLabel>
        );

      case "DATE":
        return (
          <DatePicker
            key={key}
            name={slug}
            control={control}
            startDate={new Date()}
            dateInputProps={{
              helperText: `Select ${name.toLowerCase()}`,
            }}
          />
        );

      case "IMAGE":
        return (
          <ButtonFileUpload
            key={key}
            name={slug}
            control={control}
            label={`Upload ${name}`}
            accept="image/*"
            multiple={true}
          />
        );

      default:
        return (
          <Input
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            isRequired={is_required}
            placeholder={`Enter ${toHumanReadable(name).toLowerCase()}...`}
          />
        );
    }
  };

  const renderAssetCategoryUpload = (assetCategoryList: any) => {
    const acceptTypes = (allowed_types: []) =>
      allowed_types
        .map((type: string) => {
          if (type === "pdf") return ".pdf";
          if (type === "jpg") return ".jpg,.jpeg";
          if (type === "png") return ".png";
          return `.${type}`;
        })
        .join(",");

    const fileUploadArray = assetCategoryList.map((assetCategory: any, index: number) => ({
      index: index,
      key: `asset-${assetCategory.slug}-${assetCategory.id}`,
      name: assetCategory.slug,
      control: control,
      label: `Upload ${assetCategory.name}`,
      accept: acceptTypes,
      multiple: true,
    }));
    console.log(fileUploadArray);
    return <ButtonFileUpload {...fileUploadArray[0]} />;
  };

  const transformAndSubmit = async (formData: any) => {
    setLoading(true);

    // Create a deep copy of the formSchema to avoid mutating the original
    const updatedSchema = JSON.parse(JSON.stringify(formSchema));

    // Update product_attributes with form values
    updatedSchema.product_attributes = updatedSchema.product_attributes.map((attr: any) => ({
      ...attr,
      value: formData[attr.slug] || attr.value || null,
    }));

    // Update category_attributes with form values
    updatedSchema.category_attributes = updatedSchema.category_attributes.map((attr: any) => ({
      ...attr,
      value: formData[attr.slug] || attr.value || null,
    }));

    // Update marketplace_asset_categories with form values if needed
    updatedSchema.marketplace_asset_categories = updatedSchema.marketplace_asset_categories.map(
      (assetCategory: any) => ({
        ...assetCategory,
        value: formData[assetCategory.slug] || assetCategory.value || null,
      }),
    );

    console.log("Form Data:", formData);
    console.log("Updated Schema with Values:", updatedSchema);

    await productService.createProduct(updatedSchema);
    setLoading(false);
    navigate(generateAppPath(AppPaths.inventory));
  };

  return (
    <div css={styles.wrapper}>
      <RDSButton
        css={styles.button}
        variant="tertiary"
        size="lg"
        text="Back to inventories"
        leadIcon="left_arrow"
        onClick={handleInventoryNav}
      />
      <div css={styles.sectionsWrapper}>
        <form css={[styles.form, styles.sectionLayout]} onSubmit={handleSubmit(transformAndSubmit)}>
          {formSchema.product_attributes.length > 0 && (
            <Section heading="PRODUCT ATTRIBUTES">
              {formSchema.product_attributes.sort((a, b) => a.order - b.order).map(renderFormField)}
            </Section>
          )}

          {formSchema.category_attributes.length > 0 && (
            <Section heading="CATEGORY ATTRIBUTES">
              {formSchema.category_attributes
                .sort((a, b) => a.order - b.order)
                .map(renderFormField)}
            </Section>
          )}

          {formSchema.marketplace_asset_categories.length > 0 && (
            <Section heading="ATTACHMENTS">
              {renderAssetCategoryUpload(formSchema.marketplace_asset_categories)}
            </Section>
          )}

          {/* Submit Button
          <RDSButton
            variant="primary"
            size="lg"
            text="Save Product"
            type="submit"
            loading={loading}
          /> */}
        </form>
        <div css={[styles.infoSections]}>
            <Section heading="Actions">
              <div css={styles.internalWrapper}>
                <RDSTypography
                  css={[
                    styles.actionsHeadingText,
                    fieldsError && { color: theme.rds.color.text.functional.danger.tertiary },
                  ]}
                >
                  {statusMessage}
                </RDSTypography>
                <RDSButton
                  variant="primary"
                  loading={loading}
                  onClick={handleSubmit(transformAndSubmit)}
                  size="lg"
                  text="Save Changes"
                  disabled={!isValid}
                />
                <RDSButton
                  variant="secondary"
                  size="lg"
                  onClick={handleDiscard}
                  text="Discard Changes"
                />
              </div>
            </Section>

            <Section heading="UNIT VISIBILITY" tag={{ label: "Hidden", appearance: "neutral" }}>
              <div css={styles.hideShowUnitWrapper}>
                <RDSTypography fontName={theme?.rds?.typographies?.label?.md}>
                Show to Customers
                <RDSTypography fontName={theme?.rds?.typographies?.label?.sm}>
                  Saved changes update the live unit instantly.
                  </RDSTypography>
                </RDSTypography>
                <RDSSwitch
                  checked={false}
                  disabled={!isValid}
                  onChange={() => {}}
                />
              </div>
            </Section>

            <Section heading="Information">
              <div css={styles.actionsLayout}>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Created on</RDSTypography>
                  <RDSTypography css={styles.infoDes}>--</RDSTypography>
                </div>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Created by</RDSTypography>
                  <RDSTypography css={styles.infoDes}>--</RDSTypography>
                </div>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Updated on</RDSTypography>
                  <RDSTypography css={styles.infoDes}>--</RDSTypography>
                </div>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Updated by</RDSTypography>
                  <RDSTypography css={styles.infoDes}>--</RDSTypography>
                </div>
              </div>
            </Section>
          </div>
      </div>
    </div>
  );
}
