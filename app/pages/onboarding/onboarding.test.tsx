import { screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it } from "vitest";

import { renderWithTheme } from "test/helpers/render-with-theme";

import Onboard from "./onboarding";

const setup = () => {
  const user = userEvent.setup();
  renderWithTheme(<Onboard />);
  return { user };
};

describe("Onboard Form", () => {
  it("renders initial step with personal info fields", () => {
    setup();

    expect(screen.getByText("Hello! Let’s get you started with ROSHN")).toBeInTheDocument();
    expect(screen.getByTestId("first-name")).toBeInTheDocument();
    expect(screen.getByTestId("job-title")).toBeInTheDocument();
    expect(screen.getByTestId("work-email")).toBeInTheDocument();
    expect(screen.getByTestId("phone-number")).toBeInTheDocument();
  });

  it("disables Continue button when fields are empty", () => {
    setup();

    const continueBtn = screen.getByRole("button", { name: /Continue/i });
    expect(continueBtn).toBeDisabled();
  });

  it("validates email and phone and moves to next step", async () => {
    const { user } = setup();

    await user.type(screen.getByTestId("first-name"), "Tarun");
    await user.type(screen.getByTestId("job-title"), "Engineer");
    await user.type(screen.getByTestId("job-title"), "<EMAIL>");
    await user.type(screen.getByTestId("phone-number"), "9876543210");

    const continueBtn = screen.getByRole("button", { name: /Continue/i });
    expect(continueBtn).toBeDisabled();
  });

  it("shows validation errors for invalid phone/email", async () => {
    const { user } = setup();

    await user.type(screen.getByTestId("first-name"), "Tarun");
    await user.type(screen.getByTestId("job-title"), "Engineer");
    await user.type(screen.getByTestId("work-email"), "bademail");
    await user.type(screen.getByTestId("phone-number"), "123");

    await user.tab(); // trigger blur validation

    expect(await screen.findByText("Invalid email address")).toBeInTheDocument();
    expect(await screen.findByText("Enter a valid 10-digit phone number")).toBeInTheDocument();
  });

  it("goes to completion step after company info", async () => {
    const { user } = setup();

    // Fill Personal Info
    await user.type(screen.getByTestId("first-name"), "Tarun");
    await user.type(screen.getByTestId("job-title"), "Engineer");
    await user.type(screen.getByTestId("work-email"), "<EMAIL>");
    await user.type(screen.getByTestId("phone-number"), "9876543210");
    await user.click(screen.getByRole("button", { name: /Continue/i }));

    // Fill Company Info
    await user.type(screen.getByTestId("business-type"), "IT");
    await user.type(screen.getByTestId("company-name"), "ACME Corp");
    await user.type(screen.getByTestId("company-reg-number"), "1234567890");
    await user.type(screen.getByTestId("portfolio-url"), "https://example.com");

    await user.click(screen.getByRole("button", { name: /Continue/i }));

    expect(await screen.findByText("Enrollment submitted successfully!")).toBeInTheDocument();
    expect(
      screen.getByText("Need help? Our support team is here for you anytime."),
    ).toBeInTheDocument();
  });

  it("opens and closes the cancel modal", async () => {
    const { user } = setup();

    await user.click(screen.getByRole("button", { name: /Cancel/i }));
    expect(screen.getByText("Exit enrollment?")).toBeInTheDocument();

    await user.click(screen.getByRole("button", { name: /No, continue working/i }));
    expect(screen.queryByText("Exit enrollment?")).not.toBeInTheDocument();
  });
});
