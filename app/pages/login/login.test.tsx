import { screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, vi } from "vitest";

import { renderWithTheme } from "test/helpers/render-with-theme";

import LoginPage from "./login";

const mockSignIn = vi.fn();
vi.mock("~/hooks/use-di", () => ({
  useInjection: () => ({
    signIn: mockSignIn,
  }),
}));

describe("LoginPage", () => {
  const setup = () => {
    const user = userEvent.setup();
    renderWithTheme(<LoginPage />);
    return { user };
  };

  it("renders heading and description", () => {
    setup();

    expect(screen.getByText("Welcome to ROSHN Sellers!")).toBeInTheDocument();
    expect(
      screen.getByText(/Manage your product inventory, track performance/i),
    ).toBeInTheDocument();
  });

  it("renders email and password inputs", () => {
    setup();

    expect(screen.getByTestId("email")).toBeInTheDocument();
    expect(screen.getByTestId("password")).toBeInTheDocument();
  });

  it("renders buttons and forgot password text", () => {
    setup();

    expect(screen.getByRole("button", { name: "Log in" })).toBeDisabled();
    expect(screen.getByRole("button", { name: "Start enrollment now" })).toBeInTheDocument();
    expect(screen.getByText(/Forgot password\? Reset password/)).toBeInTheDocument();
  });

  it("enables 'Log in' button on valid inputs", async () => {
    const { user } = setup();

    await user.type(screen.getByTestId("email"), "<EMAIL>");
    await user.type(screen.getByTestId("password"), "password123");

    expect(screen.getByRole("button", { name: "Log in" })).toBeEnabled();
  });

  it("displays validation error for invalid email", async () => {
    const { user } = setup();

    await user.type(screen.getByTestId("email"), "invalid-email");
    await user.tab();

    expect(await screen.findByText("Invalid email address")).toBeInTheDocument();
  });

  it("calls signIn with form values on submit", async () => {
    const { user } = setup();

    await user.type(screen.getByTestId("email"), "<EMAIL>");
    await user.type(screen.getByTestId("password"), "securePass1");

    await user.click(screen.getByRole("button", { name: "Log in" }));

    expect(mockSignIn).toHaveBeenCalledWith({
      username: "<EMAIL>",
      password: "securePass1",
    });
  });
});
