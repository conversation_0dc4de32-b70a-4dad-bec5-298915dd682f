import { screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, vi } from "vitest";

import { renderWithTheme } from "test/helpers/render-with-theme";

import ActivateAccount from "./activate-account";

vi.mock("@remix-run/react", async () => {
  const actual = await vi.importActual("@remix-run/react");
  return {
    ...actual,
    useParams: () => ({ emailAddress: "<EMAIL>" }),
  };
});

describe("ActivateAccount", () => {
  it("renders heading and description with email", () => {
    renderWithTheme(<ActivateAccount />);
    expect(screen.getByText("Activate your account")).toBeInTheDocument();
    expect(
      screen.getByText(/You are setting a new <NAME_EMAIL>/i),
    ).toBeInTheDocument();
  });

  it("renders password input fields by id", () => {
    renderWithTheme(<ActivateAccount />);
    expect(screen.getByTestId("password")).toBeInTheDocument();
    expect(screen.getByTestId("confirm-pass")).toBeInTheDocument();
  });

  it("submit button is initially disabled", () => {
    renderWithTheme(<ActivateAccount />);
    expect(screen.getByTestId("login-btn")).toBeDisabled();
  });

  it("shows error when password is too short", async () => {
    renderWithTheme(<ActivateAccount />);
    const user = userEvent.setup();

    await user.type(screen.getByTestId("password"), "a");
    await user.type(screen.getByTestId("confirm-pass"), "a");

    expect(await screen.findByText(/Password must be at least 8 characters/i)).toBeInTheDocument();
  });

  it("shows missing complexity errors", async () => {
    renderWithTheme(<ActivateAccount />);
    const user = userEvent.setup();

    await user.type(screen.getByTestId("password"), "password");
    await user.type(screen.getByTestId("confirm-pass"), "password");

    expect(
      await screen.findByText((content) => content.includes("Must include an uppercase letter")),
    ).toBeInTheDocument();

    expect(
      screen.getByText((content) =>
        content.includes("Must be at least 8 characters with letters and numbers."),
      ),
    ).toBeInTheDocument();
  });

  it("shows password mismatch error", async () => {
    renderWithTheme(<ActivateAccount />);
    const user = userEvent.setup();

    await user.type(screen.getByTestId("password"), "StrongPass1");
    await user.type(screen.getByTestId("confirm-pass"), "WrongPass1");

    expect(await screen.findByText(/Passwords do not match/i)).toBeInTheDocument();
  });

  it("passes validation when passwords are valid and match", async () => {
    renderWithTheme(<ActivateAccount />);
    const user = userEvent.setup();

    await user.type(screen.getByTestId("password"), "StrongPass1");
    await user.type(screen.getByTestId("confirm-pass"), "StrongPass1");

    expect(screen.queryByText(/Passwords do not match/)).not.toBeInTheDocument();
    expect(screen.queryByText(/Password must be at least 8 characters/)).not.toBeInTheDocument();
  });

  it("enables button on valid form state", async () => {
    renderWithTheme(<ActivateAccount />);
    const user = userEvent.setup();

    await user.type(screen.getByTestId("password"), "StrongPass1");
    await user.type(screen.getByTestId("confirm-pass"), "StrongPass1");

    expect(screen.getByTestId("login-btn")).toBeEnabled();
  });
});
