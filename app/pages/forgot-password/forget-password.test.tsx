import { screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it } from "vitest";

import { renderWithTheme } from "test/helpers/render-with-theme";

import ForgotPassword from "./forgot-password";

const setup = () => {
  renderWithTheme(<ForgotPassword />);
  const user = userEvent.setup();
  return { user };
};

describe("ForgotPassword", () => {
  it("renders email step with inputs and buttons", () => {
    setup();
    expect(screen.getByText("Forgot your password?")).toBeInTheDocument();
    expect(screen.getByTestId("email")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Send reset link" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Log in" })).toBeInTheDocument();
  });

  it("disables 'Send reset link' button for invalid email", async () => {
    const { user } = setup();
    const input = screen.getByTestId("email");
    await user.type(input, "invalid-email");
    const sendBtn = screen.getByRole("button", { name: "Send reset link" });
    expect(sendBtn).toBeDisabled();
  });

  it("enables 'Send reset link' button for valid email", async () => {
    const { user } = setup();
    const input = screen.getByTestId("email");
    await user.type(input, "<EMAIL>");
    const sendBtn = screen.getByRole("button", { name: "Send reset link" });
    expect(sendBtn).toBeEnabled();
  });

  it("proceeds to 'check-inbox' step after sending reset link", async () => {
    const { user } = setup();
    const input = screen.getByTestId("email");
    await user.type(input, "<EMAIL>");
    await user.click(screen.getByRole("button", { name: "Send reset link" }));

    expect(screen.getByText("Check your inbox!")).toBeInTheDocument();
    expect(
      screen.getByText(/We’ve sent a password reset <NAME_EMAIL>/),
    ).toBeInTheDocument();
  });

  it("proceeds to reset-password step when clicking 'Back to Login'", async () => {
    const { user } = setup();
    const input = screen.getByTestId("email");
    await user.type(input, "<EMAIL>");
    await user.click(screen.getByRole("button", { name: "Send reset link" }));
    await user.click(screen.getByRole("button", { name: "Back to Login" }));

    expect(screen.getByText("Set a new password")).toBeInTheDocument();
    expect(screen.getByTestId("password")).toBeInTheDocument();
    expect(screen.getByTestId("confirm-pass")).toBeInTheDocument();
  });

  it("shows validation error for weak password", async () => {
    const { user } = setup();
    const input = screen.getByTestId("email");
    await user.type(input, "<EMAIL>");
    await user.click(screen.getByRole("button", { name: "Send reset link" }));
    await user.click(screen.getByRole("button", { name: "Back to Login" }));

    await user.type(screen.getByTestId("password"), "abc");
    await user.type(screen.getByTestId("confirm-pass"), "abc");

    expect(await screen.findByText(/Password must be at least 8 characters/)).toBeInTheDocument();
    expect(
      screen.getByText(/Must be at least 8 characters with letters and numbers./),
    ).toBeInTheDocument();
    expect(screen.getByText(/Password must be at least 8 characters/)).toBeInTheDocument();
  });

  it("shows mismatch password error", async () => {
    const { user } = setup();
    const input = screen.getByTestId("email");
    await user.type(input, "<EMAIL>");
    await user.click(screen.getByRole("button", { name: "Send reset link" }));
    await user.click(screen.getByRole("button", { name: "Back to Login" }));

    await user.type(screen.getByTestId("password"), "StrongPass1");
    await user.type(screen.getByTestId("confirm-pass"), "Different1");

    expect(await screen.findByText(/Passwords do not match/)).toBeInTheDocument();
  });

  it("enables 'Set password and log in' button on valid password", async () => {
    const { user } = setup();
    const input = screen.getByTestId("email");
    await user.type(input, "<EMAIL>");
    await user.click(screen.getByRole("button", { name: "Send reset link" }));
    await user.click(screen.getByRole("button", { name: "Back to Login" }));

    await user.type(screen.getByTestId("password"), "StrongPass1");
    await user.type(screen.getByTestId("confirm-pass"), "StrongPass1");

    const loginBtn = screen.getByRole("button", { name: "Set password and log in" });
    expect(loginBtn).toBeEnabled();
  });
});
