import { css, useTheme } from "@emotion/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "@remix-run/react";
import {
  AppTheme,
  RDSButton,
  RDSTextInput,
  RDSTable,
  RDSTypography,
  RDSUploadFile,
  RDSProgressSteps,
  RDSModal,
  RDSEmptyState,
  RDSRadioGroup,
  RDSRadio,
} from "@roshn/ui-kit";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { DatePicker } from "~/components/form-components/date-picker/date-picker";
import { ButtonFileUpload } from "~/components/form-components/file-upload/button-file-upload";
import { Input } from "~/components/form-components/input/input";
import { TagSelector } from "~/components/form-components/tag-selector/tags-selector";
import { createSvg } from "~/components/svgs";
import { useAppPath } from "~/hooks/use-app-path";
import { AppPaths } from "~/utils/app-paths";

const Tree = createSvg(() => import("~/assets/icons/tree.svg"));
const Mosque = createSvg(() => import("~/assets/icons/mekka.svg"));
const Hospital = createSvg(() => import("~/assets/icons/hospital-building.svg"));
const Graduate = createSvg(() => import("~/assets/icons/graduate.svg"));

type StepState = "active" | "complete" | "in-complete";

type StepId = "draft" | "in-review" | "approved";

type Step = {
  stepTitle: string;
  state: StepState;
  id: StepId;
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),

  sectionWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      backgroundColor: theme.rds.color.background.ui.canvas,
      padding: theme.rds.dimension["300"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  sectionHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
      textTransform: "uppercase",
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  infoHead: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.ui.tertiary,
    }),

  infoDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.emphasis.lg,
      color: theme.rds.color.text.ui.primary,
    }),

  internalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  sectionChildrenWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  sectionDivider: (theme: AppTheme) =>
    css({
      height: "1px",
      backgroundColor: theme.rds.color.border.ui.primary,
    }),

  form: css({
    flex: "0 0 70%",
  }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      height: "fit-content",
      width: "100%",
    }),

  infoTypoWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  sectionLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  infoSections: css({
    flex: "0 0 30%",
  }),

  actionsLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),

  listingTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      color: theme.rds.color.text.ui.tertiary,
    }),

  modalDimension: css({
    "& div": {
      "& div": {
        maxWidth: "640px",
      },
    },
    "& p": {
      margin: 0,
    },
  }),

  radio: (theme: AppTheme) =>
    css({
      "& div": {
        color: theme.rds.color.text.ui.primary,
        position: "relative",
      },
      "& .roshn-boilerplate-fe-18twejl::after": {
        fontSize: "1.1rem",
        content: '" *"',
        color: theme?.rds?.color.text.functional.danger.tertiary,
      },
    }),
};

const schema = z.object({
  logo: z.any(),
  nameEn: z.any(),
  nameAR: z.any(),
  city: z.any(),
  licenseNumber: z.any(),
  handOverDate: z.any(),
  description: z.any(),
  amenities: z.any(),
  projectDocuments: z.any(),
});

const tableSchema = z.object({
  downPayment: z.any(),
  construction: z.any(),
  handover: z.any(),
});

const Section = ({ heading, children }: { heading: string; children: React.ReactNode }) => {
  return (
    <div css={styles.sectionWrapper}>
      <RDSTypography css={styles.sectionHeadingText}>{heading}</RDSTypography>
      <div css={styles.sectionDivider} />
      <div css={styles.sectionChildrenWrapper}>{children}</div>
    </div>
  );
};

export default function AddProjectPage() {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();
  const theme = useTheme() as AppTheme;

  const [showReviewModal, setReviewModal] = useState(false);
  const [showSubmitModal, setSubmitModal] = useState(false);

  const [steps, setSteps] = useState<Step[]>([
    { stepTitle: "Draft", state: "active", id: "draft" },
    { stepTitle: "In review", state: "in-complete", id: "in-review" },
    { stepTitle: "Approved", state: "in-complete", id: "approved" },
  ]);

  const { handleSubmit, control } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange",
  });

  const handleProjectNav = () => {
    navigate(generateAppPath(AppPaths.projects));
  };

  const handleProjectDetailNav = () => {
    navigate(generateAppPath(AppPaths.projectDetail));
  };

  const { control: tableControl, watch } = useForm({
    resolver: zodResolver(tableSchema),
    mode: "onChange",
    defaultValues: {
      construction: 0,
      downPayment: 0,
      handover: 0,
    },
  });

  const watchedValues = watch(["downPayment", "construction", "handover"]);

  const sum = useMemo(() => {
    const [downPayment, construction, handover] = watchedValues.map((v) =>
      isNaN(v) ? 0 : Number(v),
    );
    return downPayment + construction + handover;
  }, [watchedValues]);

  const data = [
    {
      id: "1",
      column1: { dataValue: "Down payment" },
      column2: {
        dataValue: <Input type="number" suffixText="%" control={tableControl} name="downPayment" />,
      },
    },
    {
      id: "2",
      column1: { dataValue: "During construction" },
      column2: {
        dataValue: (
          <Input type="number" suffixText="%" control={tableControl} name="construction" />
        ),
      },
    },
    {
      id: "3",
      column1: { dataValue: "On handover" },
      column2: {
        dataValue: <Input type="number" suffixText="%" control={tableControl} name="handover" />,
      },
    },
    {
      id: "4",
      column1: "",
      column2: {
        dataValue: (
          <RDSTextInput
            suffixText="%"
            type="text"
            disabled
            label="Total amount to be paid"
            helperText="This number needs to be 100."
            value={sum}
          />
        ),
      },
    },
  ];

  const tableData = {
    columns: [
      { header: "Payment Stage", accessor: "column1" },
      { header: "Percentage", accessor: "column2" },
    ],
    data,
  };

  const tagOptions = [
    {
      label: "Public Park",
      value: "park",
      disabled: false,
      leadIcon: <Tree />,
    },
    {
      label: "Mosque",
      value: "mosque",
      disabled: false,
      leadIcon: <Mosque />,
    },
    {
      label: "Health center",
      value: "health-center",
      disabled: false,
      leadIcon: <Hospital />,
    },
    {
      label: "Dinning & entertainment",
      value: "dinning-and-entertainment",
      disabled: false,
      leadIcon: <Tree />,
    },
    {
      label: "Sports ground",
      value: "sport-ground",
      disabled: false,
      leadIcon: <Tree />,
    },
    {
      label: "School",
      value: "school",
      disabled: false,
      leadIcon: <Graduate />,
    },
    {
      label: "Kindergarten",
      value: "kindergarten",
      disabled: false,
      leadIcon: <Tree />,
    },
    {
      label: "Retail center",
      value: "retail-center",
      disabled: false,
      leadIcon: <Tree />,
    },
  ];

  const [dummyLoad, setDummyLoad] = useState(false);

  useEffect(() => {
    if (dummyLoad) {
      const timer = setTimeout(() => {
        setDummyLoad(false);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [dummyLoad]);

  return (
    <>
      <div css={styles.modalDimension}>
        <RDSModal
          isOpen={showSubmitModal}
          showContent
          content={
            <RDSEmptyState
              title="Project submitted!"
              appearance="success"
              size="sm"
              description="Your project is now under review. We’ll notify you as soon as there’s an update. In the meantime, you can check your project details or return to all projects."
              buttons={[
                {
                  text: "VIEW PROJECT DETAILS",
                  variant: "primary",
                  onClick: () => {
                    handleProjectDetailNav();
                    setSubmitModal(false);
                  },
                },
                {
                  text: "BACK TO PROJECTS",
                  variant: "secondary",
                  onClick: () => {
                    handleProjectNav();
                    setSubmitModal(false);
                  },
                },
              ]}
            />
          }
        />
      </div>
      <div css={styles.modalDimension}>
        <RDSModal
          headerProps={{
            label: "Ready to submit?",
            type: "centred",
          }}
          isOpen={showReviewModal}
          buttonsGroup={{
            buttons: [
              <RDSButton
                variant="primary"
                onClick={() => {
                  setReviewModal(false);
                  setSubmitModal(true);
                }}
                text="SUBMIT FOR REVIEW"
                key="enrollment"
              />,
              <RDSButton
                variant="secondary"
                onClick={() => setReviewModal(false)}
                text="KEEP EDITING"
                key="continue"
              />,
            ],
            direction: "vertical",
          }}
          description="Once submitted, you won’t be able to edit your project while our team reviews it for approval. We’ll notify you within 2 business days about the outcome."
          showContent
          showDescription
        />
      </div>
      <div css={styles.wrapper}>
        <RDSButton
          css={styles.button}
          variant="tertiary"
          size="lg"
          text="Back to projects"
          leadIcon="left_arrow"
          onClick={handleProjectNav}
        />
        <div css={styles.sectionsWrapper}>
          <form css={[styles.form, styles.sectionLayout]} onSubmit={handleSubmit((args) => {})}>
            <Section heading="PROJECT DETAILS">
              <RDSUploadFile label="Project logo" />
              <Input
                name="nameEn"
                control={control}
                label="Project name (English)"
                isRequired
                placeholder="Example inc..."
                helperText="This name will be visible in the english version of your project page."
              />
              <div dir="rtl">
                <Input
                  name="nameAR"
                  control={control}
                  label="اسم المشروع (بالعربية)"
                  isRequired
                  placeholder="مثال: مشروع الإبتكار العقاري..."
                  helperText="سيظهر هذا الاسم في النسخة العربية من صفحة مشروعك."
                />
              </div>
              <Input
                name="city"
                control={control}
                label="City"
                isRequired
                placeholder="Select a city..."
                helperText="Specify the city where the project is located."
              />
              <Input
                name="licenseNumber"
                control={control}
                label="REGA license number"
                isRequired
                placeholder="123..."
                helperText="Your official project registration number."
              />
              <DatePicker
                startDate={new Date()}
                control={control}
                name="handOverDate"
                dateInputProps={{
                  helperText: "The date when the project will be delivered to the client.",
                  label: "Handover date",
                  isRequired: true,
                }}
                placeholderText="Select a date..."
              />
            </Section>
            <Section heading="Additional Details">
              <TagSelector
                control={control}
                name="amenities"
                description="Select available amenities in the community."
                label="Nearby amenities "
                options={tagOptions}
              />
            </Section>

            <Section heading="Attachments">
              <ButtonFileUpload control={control} name="projectDocuments" />
            </Section>

            <Section heading="Payment Plan">
              <RDSRadioGroup
                onValueChange={() => {}}
                label="Reservation fee:"
                caption="Refundable?"
                direction="horizontal"
                required
                css={styles.radio}
              >
                <RDSRadio value="yes" label="Yes" />
                <RDSRadio value="no" label="No" />
              </RDSRadioGroup>
              <RDSRadioGroup
                onValueChange={() => {}}
                caption="Fee Type"
                direction="horizontal"
                css={styles.radio}
              >
                <RDSRadio value="fixed-amount" label="Fixed amount" />
                <RDSRadio value="percentage-total" label="Percentage of total" />
              </RDSRadioGroup>
              <RDSTextInput
                label="Reservation fee amount (SAR)"
                leadIcon={<>SAR</>}
                isRequired
                placeholder="Enter price..."
                helperText="Enter the full amount to be paid for the reservation."
              />
              <RDSTypography
                css={{
                  ...theme?.rds?.typographies?.body?.emphasis?.md,
                  fontWeight: 500,
                  color: theme?.rds?.color?.text?.ui?.primary,
                }}
              >
                Payment schedule:
              </RDSTypography>
              <RDSTable
                title={tableData.title}
                description={tableData.description}
                columns={tableData.columns}
                data={tableData.data}
                pagination={false}
              />
            </Section>
          </form>
          <div css={[styles.sectionLayout, styles.infoSections]}>
            <Section heading="Actions">
              <div css={styles.internalWrapper}>
                <RDSTypography css={styles.actionsHeadingText}>
                  You have unsaved changes
                </RDSTypography>
                <RDSButton
                  variant="primary"
                  loading={dummyLoad}
                  onClick={() => setDummyLoad(true)}
                  size="lg"
                  text="Save Changes"
                />
                <RDSButton variant="secondary" size="lg" text="Discard Changes" />
              </div>
            </Section>

            <Section heading="LISTING STATUS">
              <div css={styles.internalWrapper}>
                <RDSProgressSteps type="number" steps={steps} size="md" />
                <RDSTypography css={styles.actionsHeadingText}>
                  Your project is not visible to customers. Please complete all required fields
                  before submitting for review.
                </RDSTypography>
                <RDSButton
                  variant="primary"
                  size="lg"
                  text="SUBMIT FOR REVIEW"
                  onClick={() => setReviewModal(true)}
                />
              </div>
            </Section>

            <Section heading="Information">
              <div css={styles.actionsLayout}>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Created on</RDSTypography>
                  <RDSTypography css={styles.infoDes}>Jun 20, 2025, 14:35</RDSTypography>
                </div>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Created by</RDSTypography>
                  <RDSTypography css={styles.infoDes}>Name Last name</RDSTypography>
                </div>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Updated on</RDSTypography>
                  <RDSTypography css={styles.infoDes}>Jun 24, 2025, 10:00</RDSTypography>
                </div>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Updated by</RDSTypography>
                  <RDSTypography css={styles.infoDes}>Name Last name</RDSTypography>
                </div>
              </div>
            </Section>
          </div>
        </div>
      </div>
    </>
  );
}
