import { css } from "@emotion/react";
import { useNavigate } from "@remix-run/react";
import { AppTheme, R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, RDS<PERSON>agBasic, RDSTypography } from "@roshn/ui-kit";

import Logo from "~/assets/logos/beyond-al-nakheel.svg?url";
import { createSvg } from "~/components/svgs";
import UnitListSection from "~/features/unit-list/unit-list-section";
import { useAppPath } from "~/hooks/use-app-path";
import { AppPaths } from "~/utils/app-paths";

const Tree = createSvg(() => import("~/assets/icons/tree.svg"));
const Mosque = createSvg(() => import("~/assets/icons/mekka.svg"));
const Hospital = createSvg(() => import("~/assets/icons/hospital-building.svg"));
const Graduate = createSvg(() => import("~/assets/icons/graduate.svg"));
const File = createSvg(() => import("~/assets/icons/file.svg"));
const Image = createSvg(() => import("~/assets/icons/image-gallery.svg"));

const styles = {
  divider: (theme: AppTheme) =>
    css({
      height: "24px",
      border: `1px solid ${theme.rds.color.border.ui.secondary}`,
    }),

  tagsWrapper: css({ display: "flex", flexWrap: "wrap", gap: "16px" }),

  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  additionalItemsWrapper: css({
    display: "flex",
    gap: "2rem",
  }),

  additionalContent: css({
    display: "flex",
    flexDirection: "column",
    gap: "2rem",
    textWrap: "nowrap",
  }),

  attachments: css({ display: "flex", alignItems: "center", gap: "16px" }),

  attachmentItem: css({
    display: "flex",
    gap: "8px",
    justifyContent: "center",
    alignItems: "center",
  }),

  attachmentsTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.md,
      ...theme.rds.brand.font.fontFamily.label,
      ...theme.rds.font.fontSize["87"],
      fontWeight: 300,
      color: theme.rds.color.text.brand.primary.default,
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),

  sectionWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      backgroundColor: theme.rds.color.background.ui.canvas,
      padding: theme.rds.dimension["300"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  detailWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  itemsWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  detailHeading: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.display.d5,
      ...theme.rds.brand.font.fontFamily.display,
      fontWeight: 300,
      color: theme.rds.color.text.ui.primary,
    }),

  detailSubHeading: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md5,
      ...theme.rds.brand.font.fontFamily.body,
      color: theme.rds.color.text.ui.primary,
    }),

  license: (theme: AppTheme) =>
    css({
      ...theme.rds.brand.font.fontFamily.label,
      ...theme.rds.font.fontSize["100"],
      textDecoration: "underline",
    }),

  expectedStyles: (theme: AppTheme) =>
    css({
      color: theme.rds.color.text.ui.tertiary,
    }),

  detailDescription: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.lg,
      fontWeight: 400,
      color: theme.rds.color.text.ui.primary,
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  internalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  sectionChildrenWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  sectionDivider: (theme: AppTheme) =>
    css({
      height: "1px",
      backgroundColor: theme.rds.color.border.ui.primary,
    }),

  detailSubHeadingWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      justifyContent: "space-between",
    }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["400"],
      height: "fit-content",
      width: "100%",
    }),

  infoTypoWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  sectionLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  infoSections: css({
    flex: "0 0 30%",
  }),

  additionalDetailHeading: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h4,
      ...theme.rds.font.fontSize["150"],
      color: theme.rds.color.text.ui.primary,
    }),
};

const Section = ({ heading, children }: { heading: string; children: React.ReactNode }) => {
  return (
    <div css={styles.sectionWrapper}>
      <RDSTypography css={styles.sectionHeadingText}>{heading}</RDSTypography>
      <div css={styles.sectionDivider} />
      <div css={styles.sectionChildrenWrapper}>{children}</div>
    </div>
  );
};

const config = {
  product1: {
    community: "Beyond Al-Nakheel",
    expectedDelivery: "01/09/2029",
    description:
      `Beyond is a visionary real estate development company based in Riyadh, Saudi Arabia, specializing in the creation of innovative, sustainable, and community-focused residential projects across the Kingdom. From design and construction to property management and investment services, Beyond is redefining the future of living in Saudi Arabia.
Guided by the principles of quality, affordability, and long-term value, Beyond is committed to building more than just homes—it builds opportunities for better living. Each project blends modern architecture with cultural authenticity, supporting the social and environmental goals of Saudi Vision 2030.
      `,
    license: "305",
    locality: "Al-Ahsa, Eastern Region",
  },
};

const tagOptions = [
  {
    label: "Public Park",
    value: "park",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "Mosque",
    value: "mosque",
    disabled: false,
    leadIcon: <Mosque />,
  },
  {
    label: "Health center",
    value: "health-center",
    disabled: false,
    leadIcon: <Hospital />,
  },

  {
    label: "Sports ground",
    value: "sport-ground",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "School",
    value: "school",
    disabled: false,
    leadIcon: <Graduate />,
  },
  {
    label: "Kindergarten",
    value: "kindergarten",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "Retail center",
    value: "retail-center",
    disabled: false,
    leadIcon: <Tree />,
  },
];
export default function ProjectDetail() {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();

  const details = config.product1;

  const handleProjectNav = () => {
    navigate(generateAppPath(AppPaths.projects));
  };

  const handleProjectEditNav = () => {
    navigate(generateAppPath(AppPaths.addProject));
  };

  return (
    <div css={styles.wrapper}>
      <RDSButton
        css={styles.button}
        variant="tertiary"
        size="lg"
        text="Back to project list"
        leadIcon="left_arrow"
        onClick={handleProjectNav}
      />
      <div css={styles.sectionsWrapper}>
        <div css={styles.detailWrapper}>
          <div css={styles.itemsWrapper}>
            <div css={styles.detailSubHeadingWrapper}>
              <RDSTypography css={styles.detailHeading}>{details.community}</RDSTypography>
              <img src={Logo} style={{ width: "70px", alignSelf: "stretch" }} alt="Roshn Logo" />
            </div>
            <div css={styles.detailSubHeadingWrapper}>
              <RDSTypography css={styles.detailSubHeading}>
                {`${details.locality}`}
                {
                  <span css={styles.expectedStyles}>
                    (Expected for: {details.expectedDelivery})
                  </span>
                }
              </RDSTypography>
              <RDSTypography css={[styles.detailSubHeading, styles.license]}>
                {`REGA license: ${details.license}`}
              </RDSTypography>
            </div>
            <RDSTypography css={styles.detailDescription}>{details.description}</RDSTypography>
          </div>
          <div css={styles.itemsWrapper}>
            <RDSTypography css={styles.additionalDetailHeading}>Additional details</RDSTypography>

            <div css={styles.additionalItemsWrapper}>
              <div css={styles.additionalContent}>
                <RDSTypography>Attachments:</RDSTypography>
                <RDSTypography>Nearby amenities:</RDSTypography>
              </div>
              <div css={styles.additionalContent}>
                <div css={styles.attachments}>
                  <div css={styles.attachmentItem}>
                    <RDSAssetWrapper>
                      <File />
                    </RDSAssetWrapper>
                    <RDSTypography css={styles.attachmentsTypo}>Brochure</RDSTypography>
                  </div>
                  <div css={styles.divider} />
                  <div css={styles.attachmentItem}>
                    <RDSAssetWrapper>
                      <Image />
                    </RDSAssetWrapper>
                    <RDSTypography css={styles.attachmentsTypo}>Masterplane</RDSTypography>
                  </div>
                  <div css={styles.divider} />
                  <div css={styles.attachmentItem}>
                    <RDSAssetWrapper>
                      <Image />
                    </RDSAssetWrapper>
                    <RDSTypography css={styles.attachmentsTypo}>Floor plans</RDSTypography>
                  </div>
                </div>
                <div css={styles.tagsWrapper}>
                  {tagOptions.map(({ leadIcon, label }, index) => (
                    <RDSTagBasic
                      leadIcon={leadIcon}
                      key={index}
                      label={label}
                      appearance="neutral"
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div css={[styles.sectionLayout, styles.infoSections]}>
          <Section heading="LISTING STATUS">
            <div css={styles.internalWrapper}>
              <RDSTypography css={styles.actionsHeadingText}>
                Please complete all required fields before submitting for review.
              </RDSTypography>
              <RDSButton
                variant="primary"
                size="lg"
                text="CONTINUE EDITING"
                onClick={handleProjectEditNav}
              />
            </div>
          </Section>
          <Section heading="DELETE PROJECT">
            <div css={styles.internalWrapper}>
              <RDSTypography css={styles.actionsHeadingText}>
                Deleting this project is permanent. Consider hiding it from public visibility
                instead.
              </RDSTypography>
              <RDSButton variant="secondary" size="lg" text="DELETE PROJECT" />
            </div>
          </Section>
        </div>
      </div>
      <UnitListSection />
    </div>
  );
}
