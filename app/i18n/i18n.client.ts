import { createInstance } from "i18next";
import type { i18n as I18N, InitOptions } from "i18next";
import ChainedBackend from "i18next-chained-backend";
import type { Container } from "inversify";
import { initReactI18next } from "react-i18next";

import { i18nOptions, SUPPORTED_LANGUAGES } from "./i18n-options";
import { TranslationBackend } from "./init-i18n-backend";
import { LocalFileBackend } from "./local-file-backend";

export const i18n = createInstance();

export const initI18nInstanceClient = async (
  instance: I18N,
  container: Container,
  init: InitOptions = {},
) => {
  await instance
    .use(initReactI18next)
    .use(new ChainedBackend())
    .init({
      backend: {
        backendOptions: [
          {
            container,
          },
        ],
        backends: [
          TranslationBackend,
          LocalFileBackend, // fallback
        ],
      },
      preload: SUPPORTED_LANGUAGES,
      ...i18nOptions,
      ...init,
    });
};

export async function setupI18nClient(i18n: I18N, lng: string, container: Container) {
  await initI18nInstanceClient(i18n, container, {
    lng,
  });

  return i18n;
}
