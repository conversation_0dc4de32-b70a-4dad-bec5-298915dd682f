// Available languages
export const SUPPORTED_LANGUAGES = ["en", "ar"] as const;
export type SupportedLanguage = (typeof SUPPORTED_LANGUAGES)[number];
export const DEFAULT_LANGUAGE: SupportedLanguage = "en";

// Available namespaces
export const NAMESPACES = ["home-acquisition"] as const;
export type Namespace = (typeof NAMESPACES)[number];

export const i18nOptions = {
  // debug: process.env.NODE_ENV !== "production",
  fallbackLng: DEFAULT_LANGUAGE,
  supportedLngs: SUPPORTED_LANGUAGES,
  defaultNS: "home-acquisition",
  ns: NAMESPACES,
  react: { useSuspense: false },
};
