import type { i18n as I18N, InitOptions } from "i18next";
import { createInstance } from "i18next";
import ChainedBackend from "i18next-chained-backend";
import FsBackend from "i18next-fs-backend";
import type { Container } from "inversify";
import { initReactI18next } from "react-i18next";

import { bindImplsServer } from "~/di/bind-impls.server";

import { createContainer } from "../di/container";

import { i18nOptions } from "./i18n-options";
import { TranslationBackend } from "./init-i18n-backend";

export const initI18nInstanceServer = async (
  instance: I18N,
  container: Container,
  init: InitOptions = {},
  cacheFilePattern = `./locales_cache/{{lng}}.json`,
) => {
  await instance
    .use(initReactI18next)
    .use(new ChainedBackend())
    .init({
      backend: {
        backendOptions: [
          {
            addPath: cacheFilePattern,
            // Set appropriate expiration time for cached translations
            expirationTime: 60_000,
            loadPath: cacheFilePattern,
          },
          {
            container,
          },
        ],
        backends: [
          FsBackend,
          TranslationBackend, // fallback
        ],
      },
      ...i18nOptions,
      ...init,
    });
};

export async function setupI18nServer(lng: string, container: Container) {
  if (!container) {
    container = createContainer();
    bindImplsServer(container, lng);
  }
  const instance = createInstance();
  await initI18nInstanceServer(instance, container, {
    lng,
  });

  return instance;
}
