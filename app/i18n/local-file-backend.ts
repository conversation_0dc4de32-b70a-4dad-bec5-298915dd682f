import type { BackendModule, ReadCallback, Services } from "i18next";

import type { SupportedLanguage, Namespace } from "./i18n-options";

// This type ensures all supported languages are represented
export type TranslationFiles = {
  [K in SupportedLanguage]: {
    [N in Namespace]: () => Promise<{ default: Record<string, any> }>;
  };
};

// This enforces that all combinations exist - TypeScript will error if any are missing
export const translationImports: TranslationFiles = {
  en: {
    "home-acquisition": () => import("./locales/en/home-acquisition.json"),
  },
  ar: {
    "home-acquisition": () => import("./locales/ar/home-acquisition.json"),
  },
};

// Helper function to get translation dynamically with full type safety
export const getTranslationImporter = (language: SupportedLanguage, namespace: Namespace) => {
  return translationImports[language][namespace];
};

// Type-safe way to check if a translation exists
export const hasTranslation = (
  language: string,
  namespace: string,
): language is SupportedLanguage => {
  return (
    language in translationImports &&
    namespace in translationImports[language as keyof typeof translationImports]
  );
};

export class LocalFileBackend implements BackendModule<Record<string, unknown>> {
  type = "backend" as const;

  init(_services: Services, _backendOptions: Record<string, unknown>): void {
    // No initialization needed
  }

  async read(language: string, namespace: string, callback: ReadCallback): Promise<void> {
    try {
      // Type-safe check if translation exists
      if (!hasTranslation(language, namespace)) {
        throw new Error(`Unsupported language/namespace combination: ${language}/${namespace}`);
      }

      // After the type guard, we know these are valid
      const importTranslation = getTranslationImporter(
        language as SupportedLanguage,
        namespace as Namespace,
      );

      // Load the translation
      const module = await importTranslation();

      // Return the translation data
      callback(null, module.default || module);
    } catch (error) {
      console.error(`Error loading translation ${language}/${namespace}:`, error);
      callback(error as Error, null);
    }
  }
}
