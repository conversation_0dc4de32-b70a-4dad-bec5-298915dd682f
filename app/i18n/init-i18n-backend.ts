import type { BackendModule, Services, ReadCallback } from "i18next";
import type { Container } from "inversify";

import { TranslationService } from "../services/translation";

export class TranslationBackend implements BackendModule<{ container: Container }> {
  type = "backend" as const;

  private container!: Container;
  init(services: Services, backendOptions: { container: Container }): void {
    this.container = backendOptions.container;
  }
  async read(language: string, namespace: string, callback: ReadCallback): Promise<void> {
    try {
      const translationService = this.container.get<TranslationService>(TranslationService);
      const response = await translationService.getTranslation(language, namespace);
      const t = response.attributes.metadata;
      if (t) {
        callback(null, t);
      }
    } catch (err) {
      callback(err as Error, null);
    }
  }
}
