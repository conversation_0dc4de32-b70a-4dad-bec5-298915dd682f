/**
 * By default, <PERSON> will handle hydrating your app on the client for you.
 * You are free to delete this file if you'd like to, but if you ever want it revealed again, you can run `npx remix reveal` ✨
 * For more information, see https://remix.run/file-conventions/entry.client
 */
import "reflect-metadata/lite";
import { RemixBrowser } from "@remix-run/react";
import { startTransition, StrictMode } from "react";
import { hydrateRoot } from "react-dom/client";
import { I18nextProvider } from "react-i18next";

import { bindImplsClient } from "./di/bind-impls.client";
import { appContainer } from "./di/create-client-container";
import { DIProvider } from "./hooks/use-di";
import { setupI18nClient, i18n } from "./i18n/i18n.client";
import { getLangFromUrl } from "./utils/get-lang";

if (import.meta.env.MODE === "development" && import.meta.env.VITE_ENABLE_MSW) {
  const { worker } = await import("./mocks/browser");
  await worker.start({
    onUnhandledRequest: "warn", // show warning if <PERSON><PERSON> misses a request
  });
}

bindImplsClient(appContainer);

const hydrate = async () => {
  const lng = getLangFromUrl(location.href);
  await setupI18nClient(i18n, lng, appContainer);

  startTransition(() => {
    hydrateRoot(
      document,
      <I18nextProvider i18n={i18n}>
        <DIProvider container={appContainer}>
          <StrictMode>
            <RemixBrowser />
          </StrictMode>
        </DIProvider>
      </I18nextProvider>,
    );
  });
};

hydrate();
