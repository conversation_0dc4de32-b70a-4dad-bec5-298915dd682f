import { css, useTheme } from "@emotion/react";
import { useNavigate } from "@remix-run/react";
import {
  RDSTable,
  RDSTagInteractive,
  RDSButton,
  RDSTypography,
  AppTheme,
  RDSSearchInput,
  RDSModal,
  RDSUploadFile,
  RDSAssetWrapper,
} from "@roshn/ui-kit";
import { useEffect, useMemo, useState } from "react";

import { Popover } from "~/components/popover/popover";
import { createSvg } from "~/components/svgs";
import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";
import { useAppPath } from "~/hooks/use-app-path";
import { QueryProductListParams, useProductList } from "~/hooks/use-product-list";
import { Product } from "~/services/product-list/product-list";
import { AppPaths } from "~/utils/app-paths";
import { formatCurrency } from "~/utils/helper";

const Upload = createSvg(() => import("~/assets/icons/upload.svg"));

const tagData = [
  { label: "All", state: "active" },
  { label: "Approved", state: "default" },
];

const dummyData = [
  {
    title: "BEYOND-AE-101",
    status: "ACTIVE",
    price: "51850.00",
    custom_attributes: [
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      { label: "project", value: "Beyond Horizon" },
    ],
  },
  {
    title: "BEYOND-AQ-202",
    status: "DRAFT",
    price: "51675.25",
    custom_attributes: [
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      { label: "project", value: "Beyond Horizon" },
    ],
  },
  {
    title: "BEYOND-AG-303",
    status: "INACTIVE",
    price: "50940.00",
    custom_attributes: [
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      { label: "project", value: "Beyond Horizon" },
    ],
  },
  {
    title: "BEYOND-AE-404",
    status: "ACTIVE",
    price: "52130.75",
    custom_attributes: [
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      { label: "project", value: "Beyond Horizon" },
    ],
  },
  {
    title: "BEYOND-AQ-505",
    status: "APPROVED",
    price: "51299.00",
    custom_attributes: [
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      { label: "project", value: "Beyond Horizon" },
    ],
  },
  {
    title: "BEYOND-AG-606",
    status: "ACTIVE",
    price: "50850.00",
    custom_attributes: [
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      { label: "project", value: "Beyond Horizon" },
    ],
  },
  {
    title: "BEYOND-AE-707",
    status: "INACTIVE",
    price: "50715.00",
    custom_attributes: [
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      { label: "project", value: "Beyond Horizon" },
    ],
  },
  {
    title: "BEYOND-AQ-808",
    status: "ACTIVE",
    price: "51055.49",
    custom_attributes: [
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      { label: "project", value: "Beyond Horizon" },
    ],
  },
];
const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      background: theme?.rds?.color.background.brand.secondary.inverse.default,
      minHeight: "100vh",
      width: "100%",
    }),
  header: (theme: AppTheme) =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      paddingBottom: theme?.rds?.dimension["200"],
    }),
    ctaContainer: (theme: AppTheme) => css({ 
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      gap: theme?.rds?.dimension["200"],
     }),
  searchInput: (theme: AppTheme) =>
    css({
      gap: theme.rds.dimension["200"],
    }),
  tagContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      marginTop: theme.rds.dimension["300"],
    }),

  modalDimension: css({
    "& div": {
      "& div": {
        maxWidth: "640px",
      },
    },
  }),
};

const AddUnits = ({ onSuccess }: { onSuccess: () => void }) => {
  const [uploadModal, setUploadModal] = useState(false);
  const [inpFile, setInpFile] = useState<unknown>();
  const [loading, setLoading] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setInpFile(file);
    document.getElementsByClassName("roshn-boilerplate-fe-1jke4yk")[0].remove();
    if (!file) return;
  };

  const handelCloseModal = () => {
    setUploadModal(false);
    setInpFile(null);
  };

  useEffect(() => {
    if (loading) {
      const timer = setTimeout(() => {
        setLoading(false);
        handelCloseModal();
        onSuccess();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [loading]);

  return (
    <>
      <RDSButton
        css={{ textTransform: "none" }}
        variant="secondary"
        size="lg"
        text="IMPORT UNITS"
        data-testid="upload-button"
        onClick={() => setUploadModal(true)}
        leadIcon={
          <RDSAssetWrapper>
            <Upload />
          </RDSAssetWrapper>
        }
      />
      <div css={styles.modalDimension}>
        <RDSModal
          headerProps={{
            label: "Upload File",
            type: "centred",
          }}
          showContent
          showDescription
          description="Add multiple units in bulk by uploading a CSV file. Ensure your file follows the required format to correctly import property details like unit id, status, project and price availability."
          isOpen={uploadModal}
          content={
            <RDSUploadFile
              onChange={(e) => handleFileChange(e)}
              onDismissHandler={() => setInpFile(null)}
              accept=".csv"
            />
          }
          footer
          buttonsGroup={{
            buttons: [
              <RDSButton
                variant="primary"
                onClick={() => setLoading(true)}
                text="SUBMIT FILES"
                disabled={!inpFile}
                loading={loading}
                key="enrollment"
              />,
              <RDSButton
                variant="secondary"
                onClick={handelCloseModal}
                text="BACK TO INVENTORY"
                key="continue"
              />,
            ],
            direction: "vertical",
          }}
        />
      </div>
    </>
  );
};

export default function UnitListSection() {
  const theme = useTheme() as AppTheme;
  const [activeTag, setActiveTag] = useState("All");
  const [search, setSearch] = useState({ searchQuery: "" });
  const [searchParam, setSearchParam] = useState({ search: "" });
  const generatePath = useAppPath();
  const navigate = useNavigate();

  const productListParams: QueryProductListParams = {
    marketplace_product_status: activeTag === "All" ? "" : "APPROVED",
    search: searchParam.search,
  };

  const { data, isFetching, isSuccess } = useProductList(productListParams);

  const [productList, setProductList] = useState<unknown>([]);

  useEffect(() => {
    if (isSuccess) {
      setProductList(data?.results);
    }
  }, [isSuccess]);

  const leadIconProduct = () => (
    <img
      height="28px"
      width="88px"
      src="https://************.nip.io/roshn_group_logo_with_text_4b8be975b2/roshn_group_logo_with_text_4b8be975b2.svg"
      alt="Roshn Logo"
    />
  );

  const tableColumnData = useMemo(
    () =>
      productList.map((product: Product, index: number) => ({
        id: { dataValue: `row-${index}` },
        column1: {
          dataValue: (
            <a href="https://home-dev.myroshn.com/en" css={{ cursor: "pointer" }}>
              {product?.title}
            </a>
          ),
          leadIcon: leadIconProduct,
        },
        column2: {
          dataValue: product.status,
          tagType: product.status === "ACTIVE" ? "success" : "info",
        },
        column3: { dataValue: "Beyond Horizon" },
        column4: { dataValue: formatCurrency(product?.price, "en-US", "SAR") },
      })),
    [productList],
  );

  const tableData = {
    columns: [
      {
        id: "column1",
        header: "Unit Id",
        accessor: "column1",
        type: "lead",
      },
      {
        id: "column2",
        header: "Status",
        accessor: "column2",
        type: "tag",
      },
      {
        id: "column3",
        header: "Project",
        accessor: "column3",
        type: "text",
      },
      {
        id: "column4",
        header: "Price",
        accessor: "column4",
        type: "text",
      },
      {
        id: "actions",
        header: "Actions",
        accessor: "id",
        type: "action",
      },
    ],
    data: tableColumnData,
  };

  return (
    <div css={styles.wrapper(theme)}>
      <div css={styles.header(theme)}>
        <div css={{ ...theme?.rds?.typographies?.heading?.emphasis?.h4 }}>Listed units (0)</div>
        <div css={styles.ctaContainer}>
          <AddUnits
            onSuccess={() => {
              setProductList((prev) => [...dummyData, ...prev]);
            }}
          />
          <RDSButton
            size="lg"
            text="+ ADD UNIT"
            onClick={() => navigate(generatePath(AppPaths.addProduct))}
          />
        </div>
      </div>
      <div style={{ marginBottom: theme.rds.dimension["400"] }}>
        <RDSSearchInput
          type="text"
          placeholder="Search by name, city, status..."
          css={styles.searchInput(theme)}
          onChange={(e) => {
            setSearch({ searchQuery: e.target.value });
          }}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              setSearchParam({ search: search.searchQuery });
            }
          }}
        />
        <div css={styles.tagContainer(theme)}>
          {tagData.map((tag) => (
            <RDSTagInteractive
              key={tag.label}
              size="md"
              label={tag.label}
              state={tag.label === activeTag ? "active" : "default"}
              onClick={() => setActiveTag(tag.label)}
            />
          ))}
        </div>
      </div>
      {isFetching ? (
        <RoshnContainerLoader />
      ) : (
        <RDSTable
          actionText={"View Details"}
          title={tableData.title}
          description={tableData.description}
          columns={tableData.columns}
          data={tableData.data}
          pagination={false}
        />
      )}
    </div>
  );
}
