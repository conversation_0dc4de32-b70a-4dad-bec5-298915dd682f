import { useTheme } from "@emotion/react";
import { useLocation, useNavigate } from "@remix-run/react";
import { RDSDashboardSidebar } from "@roshn/ui-kit";
import { useCallback } from "react";

import { createSvg } from "~/components/svgs";
import { labelToAppPathKey } from "~/constants/global-slider-routes";
import { useAppPath } from "~/hooks/use-app-path";
import { useSideBarStore } from "~/store/useSideBarStore";
import { AppPaths } from "~/utils/app-paths";

import { SidebarState } from "./sidebar-slice";
import { AuthService } from "~/interface/authTypes";
import { useInjection } from "~/hooks/use-di";
import { AssetWrapper } from "node_modules/@roshn/ui-kit/dist/components/rds-components/asset-wrapper";

const sideBarLabels = {
  notification: "Notifications",
  profile: "My Account",
  navigate: "Navigate",
  logout: "Logout",
};

const BuildingIcon = createSvg(() => import("~/assets/icons/Building/Building.svg"));
const DashboardIcon = createSvg(() => import("~/assets/icons/Dashboard/Dashboard.svg"));
const Crane = createSvg(() => import("~/assets/icons/construction-crane.svg"));

export function GlobalSidebar() {
  const navigate = useNavigate();
  const location = useLocation();
  const auth = useInjection<AuthService>(AuthService);
  const { sidebarStatus, openSidebar, closeSidebar } = useSideBarStore();

  const generatePath = useAppPath();

  const handleClose = useCallback(() => {
    closeSidebar();
  }, []);

  const handleOpen = useCallback(() => {
    openSidebar();
  }, []);

  const handleProfileClick = useCallback(() => {
    navigate(generatePath(AppPaths.profile));
  }, [navigate]);

  const navigationLinks: { label: string; icon: React.ReactNode; active: boolean }[] = [
    {
      label: "Dashboard",
      icon: <DashboardIcon />,
      active: location.pathname === generatePath(AppPaths.dashboard),
    },
    {
      label: "Projects",
      icon: <Crane />,

      active: location.pathname === generatePath(AppPaths.projects),
    },
  ];

  const handleNavigationClick = useCallback(
    ({ label }: { label: string }) => {
      const key = labelToAppPathKey[label.toLocaleLowerCase()];
      if (key) {
        navigate(generatePath(AppPaths[key]));
      }
    },
    [navigate],
  );

  const handleLogoutClick = useCallback(() => {
    auth.signOut();
    navigate(generatePath(AppPaths.login));
  }, [auth, navigate]);

  const theme = useTheme();

  return (
    <div
      css={{
        width: sidebarStatus === SidebarState.OPEN ? "22.5rem" : "5.5rem",
        "& > div:first-of-type": {
          position: "fixed",
          zIndex: 2,
          // background: theme.rds.color.background.brand.primary.navbar,
        },
      }}
    >
      <RDSDashboardSidebar
        isOpen={sidebarStatus === SidebarState.OPEN}
        onClose={handleClose}
        onOpen={handleOpen}
        roshnLogo="https://alb-home.roshn.sa/roshn_Group_Logo_2ce51d7009/roshn_Group_Logo_2ce51d7009.svg"
        navigationLinks={navigationLinks}
        sideBarLabels={sideBarLabels}
        onNavigationClick={(e) => handleNavigationClick(e)}
        onProfileClick={handleProfileClick}
        onLogoutClick={handleLogoutClick}
      />
    </div>
  );
}
