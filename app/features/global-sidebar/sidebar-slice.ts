import { StateCreator } from "zustand";

export enum SidebarState {
  OPEN = "OPEN",
  CLOSED = "CLOSED",
}

export interface SidebarSlice {
  sidebarStatus: SidebarState;
  openSidebar: () => void;
  closeSidebar: () => void;
  toggleSidebar: () => void;
}

export const createSidebarSlice: StateCreator<SidebarSlice> = (set) => ({
  sidebarStatus: SidebarState.OPEN,
  openSidebar: () => set({ sidebarStatus: SidebarState.OPEN }),
  closeSidebar: () => set({ sidebarStatus: SidebarState.CLOSED }),
  toggleSidebar: () =>
    set((state) => ({
      sidebarStatus:
        state.sidebarStatus === SidebarState.OPEN ? SidebarState.CLOSED : SidebarState.OPEN,
    })),
});
