import { css, keyframes } from "@emotion/react";
import { AppTheme } from "@roshn/ui-kit";

const spinnerKeyframes = keyframes`
  to {
    transform: rotate(360deg);
  }
`;

const spinnerContainer = (theme: AppTheme) =>
  css({
    position: "relative",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: theme?.rds?.color.background.brand.secondary.inverse.default,
    zIndex: theme?.rds?.zIndex?.top,
  });

const spinner = (theme: AppTheme) =>
  css({
    width: "50px",
    height: "50px",
    border: "5px solid transparent",
    borderTop: `5px solid ${theme?.rds?.color?.darkGreen["400"]}`,
    borderRadius: theme?.rds?.border?.borderRadius.round,
    animation: `${spinnerKeyframes} 1s linear infinite`,
  });

export function RoshnContainerLoader() {
  return (
    <div css={spinnerContainer}>
      <div css={spinner} />
    </div>
  );
}
