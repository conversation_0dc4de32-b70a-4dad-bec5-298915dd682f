import { css, keyframes } from "@emotion/react";
import { AppTheme } from "@roshn/ui-kit";

const spinnerKeyframes = keyframes`
  to {
    transform: rotate(360deg);
  }
`;

const spinnerContainer = (theme: AppTheme) =>
  css({
    position: "fixed",
    top: 0,
    left: 0,
    width: "100vw",
    height: "100vh",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: theme?.rds?.color?.background?.ui?.primary?.default,
    zIndex: theme?.rds?.zIndex?.top,
  });

const spinner = (theme: AppTheme) =>
  css({
    width: "50px",
    height: "50px",
    border: "5px solid transparent",
    borderTop: `5px solid ${theme?.rds?.color?.darkGreen["400"]}`,
    borderRadius: theme?.rds?.border?.borderRadius.round,
    animation: `${spinnerKeyframes} 1s linear infinite`,
  });

export function RoshnLoadingModal() {
  return (
    <div css={spinnerContainer}>
      <div css={spinner} />
    </div>
  );
}
