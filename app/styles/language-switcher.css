.language-switcher {
  display: flex;
  align-items: center;
  padding: 0.5rem;
}

.select-language {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  background-color: white;
  color: #1a202c;
  font-size: 0.875rem;
  line-height: 1.25rem;
  cursor: pointer;
  outline: none;
  transition: all 0.2s;
}

.select-language:hover {
  border-color: #cbd5e0;
}

.select-language:focus {
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}

/* RTL Support */
[dir='rtl'] .language-switcher {
  direction: rtl;
}

[dir='rtl'] .select-language {
  text-align: right;
}
