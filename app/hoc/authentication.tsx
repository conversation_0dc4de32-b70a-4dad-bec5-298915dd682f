import { useNavigate, useLocation, createSearchParams } from "@remix-run/react";
import { useEffect, useState } from "react";

import { RoshnLoadingModal } from "~/features/common/loading/roshn-loading-modal";
import { useAppPath } from "~/hooks/use-app-path";
import { useStore } from "~/store/store";
import { AppPaths } from "~/utils/app-paths";

type Option = {
  redirectTo?: string;
};

export const authenticationHoc = <P extends object = object>(
  WrappedComponent: React.ComponentType,
  options: Option = {},
) => {
  const WithAuthenticated: React.FC<P> = (props) => {
    const navigate = useNavigate();
    const generateAppPath = useAppPath();
    const location = useLocation();

    const [isClient, setIsClient] = useState(false);

    useEffect(() => {
      setIsClient(true);
    }, []);

    const isLoggedIn = useStore().getState().signedIn;

    useEffect(() => {
      if (!isClient) return;
      if (isLoggedIn) return;

      const search = createSearchParams({
        redirect: location.pathname + location.search,
      }).toString();

      const pathname = options.redirectTo
        ? generateAppPath(options.redirectTo, {})
        : generateAppPath(AppPaths.login);

      navigate(
        {
          pathname,
          search: !isLoggedIn ? search : undefined,
        },
        {
          replace: true,
        },
      );
    }, [isLoggedIn, isClient]);

    if (!isClient) {
      return <RoshnLoadingModal />;
    }

    if (!isLoggedIn) {
      return null;
    }

    return <WrappedComponent {...props} />;
  };

  return WithAuthenticated;
};
