import { fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import { describe, expect, it, vi } from "vitest";

import { LanguageSwitcher } from "../language-switcher";

const mockChangeLanguage = vi.fn();

vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    i18n: {
      language: "en",
      changeLanguage: mockChangeLanguage,
    },
  }),
}));

describe("LanguageSwitcher", () => {
  it("renders the language switcher with the correct initial value", () => {
    render(<LanguageSwitcher />);
    const selectElement = screen.getByRole("combobox");
    expect(selectElement).toHaveValue("en");
  });

  it("calls changeLanguage when a new language is selected", () => {
    render(<LanguageSwitcher />);
    const selectElement = screen.getByRole("combobox");
    fireEvent.change(selectElement, { target: { value: "ar" } });
    expect(mockChangeLanguage).toHaveBeenCalledWith("ar");
  });
});
