import { useCallback } from "react";
import { useTranslation } from "react-i18next";

const languages = [
  { code: "en", name: "English", dir: "ltr" },
  { code: "ar", name: "العربية", dir: "rtl" },
];

export function LanguageSwitcher() {
  const { i18n } = useTranslation();

  const changeLanguage = useCallback(
    (languageCode: string) => {
      const language = languages.find((lang) => lang.code === languageCode);
      if (language) {
        // Change language
        i18n.changeLanguage(languageCode);

        // Update document direction
        document.documentElement.dir = language.dir;
        document.documentElement.lang = language.code;

        // Update body attributes
        document.body.setAttribute("data-direction", language.dir);
      }
    },
    [i18n],
  );

  return (
    <div className="language-switcher">
      <select
        value={i18n.language}
        onChange={(e) => changeLanguage(e.target.value)}
        className="select-language"
      >
        {languages.map((language) => (
          <option key={language.code} value={language.code}>
            {language.name}
          </option>
        ))}
      </select>
    </div>
  );
}
