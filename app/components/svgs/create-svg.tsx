/// <reference types="vite-plugin-svgr/client" />

import * as React from "react";

import { FallbackIcon } from "../icons/fallback-icon";
import { IconProps } from "../icons/icon-props";

export type SvgrComponent = typeof import("*.svg").ReactComponent;

export type CreateSvgArg = {
  defaultHeight?: string | number;
  defaultSize?: string | number;
  defaultWidth?: string | number;
};

export const createBaseSvg = (
  Component: SvgrComponent | React.LazyExoticComponent<SvgrComponent>,
  { defaultSize, defaultHeight, defaultWidth }: CreateSvgArg = {},
) => {
  const ForwardRef = React.forwardRef<SVGSVGElement, IconProps>(
    ({ size, width: widthProp, height: heightProp, ...props }, ref) => {
      const width = widthProp ?? size ?? defaultWidth ?? defaultSize;
      const height = heightProp ?? size ?? defaultHeight ?? defaultSize;

      return (
        <Component
          width={width}
          height={height}
          // legacyRef include string
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          ref={ref}
          {...props}
        />
      );
    },
  );

  ForwardRef.displayName = "BaseSvg";
  return ForwardRef;
};

export const createSvg = (
  factory: () => Promise<{ ReactComponent: SvgrComponent }>,
  { defaultSize, defaultHeight, defaultWidth }: CreateSvgArg = {},
) => {
  const LazyIcon = React.lazy(() => factory().then((mod) => ({ default: mod.ReactComponent })));

  const Comp = createBaseSvg(LazyIcon, {
    defaultHeight,
    defaultSize,
    defaultWidth,
  });

  const createSvgRef = React.forwardRef<SVGSVGElement, IconProps>(
    ({ size, width: widthProp, height: heightProp, ...props }, ref) => {
      const width = widthProp ?? size ?? defaultWidth ?? defaultSize;
      const height = heightProp ?? size ?? defaultHeight ?? defaultSize;
      return (
        <React.Suspense
          fallback={<FallbackIcon ref={ref} width={width} height={height} {...props} />}
        >
          <Comp
            width={width}
            height={height}
            // legacyRef include string
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            ref={ref}
            {...props}
          />
        </React.Suspense>
      );
    },
  );
  createSvgRef.displayName = "CreateSvg";
  return createSvgRef;
};

export type SvgComponent = ReturnType<typeof createSvg>;
