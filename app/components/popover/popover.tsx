import React, { useState } from "react";

import { usePopover } from "~/hooks/use-popover";

export const Popover = ({
  children,
  trigger,
}: {
  children: React.ReactNode;
  trigger: React.ReactNode;
}) => {
  const [open, setOpen] = useState<boolean>(false);

  const { triggerProps, layerProps, renderLayer } = usePopover({
    open,
    setOpen,
  });

  return (
    <div>
      <div onClick={() => setOpen(!open)} {...triggerProps}>
        {trigger}
      </div>

      {open &&
        renderLayer(
          <div
            css={{
              backgroundColor: "white",
              width: "fit-content",
              padding: "5px",
            }}
            {...layerProps}
          >
            {children}
          </div>,
        )}
    </div>
  );
};
