import { css } from "@emotion/react";
import { AppTheme } from "@roshn/ui-kit";

import Roshn<PERSON>ogo from "~/assets/logos/roshn-logo-new-horizontal.svg?url";

const styles = {
  authWrapper: (theme: AppTheme) =>
    css({
      padding: theme.rds.dimension["400"],
      backgroundColor: theme.rds.color.background.ui.primary.default,
      minWidth: theme.rds.dimension["4000"],
      width: "640px",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: theme.rds.dimension["300"],
      textAlign: "center",
    }),

  logo: (theme: AppTheme) =>
    css({
      width: theme.rds.dimension["1600"],
      height: "128px",
    }),
};

export function BrandWrapper({ children }: { children: React.ReactNode }) {
  return (
    <div css={styles.authWrapper}>
      <img src={RoshnLogo} alt="Roshn Logo" css={styles.logo} />
      {children}
    </div>
  );
}
