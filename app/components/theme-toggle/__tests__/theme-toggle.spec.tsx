import { useTheme } from "@emotion/react";
import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it, vi, type Mock } from "vitest";

import { ThemeToggle } from "../theme-toggle";

vi.mock("@emotion/react", async (importOriginal) => {
  const mod = await importOriginal<typeof import("@emotion/react")>();
  return {
    ...mod,
    useTheme: vi.fn(),
  };
});

const mockUseTheme = useTheme as Mock;

describe("ThemeToggle", () => {
  const mockToggleTheme = vi.fn();

  const theme = {
    mode: "light",
    rds: {
      dimension: { 400: "24px" },
      color: {
        castletonGreen: { 900: "#005a3c" },
      },
    },
    toggleTheme: mockToggleTheme,
  };

  it("renders with light mode icon and correct aria-label", () => {
    mockUseTheme.mockReturnValue(theme);
    render(<ThemeToggle />);
    expect(screen.getByLabelText("Switch to dark mode")).toBeInTheDocument();
  });

  it("renders with dark mode icon and correct aria-label", () => {
    mockUseTheme.mockReturnValue({ ...theme, mode: "dark" });
    render(<ThemeToggle />);
    expect(screen.getByLabelText("Switch to light mode")).toBeInTheDocument();
  });

  it("calls toggleTheme on click", () => {
    mockUseTheme.mockReturnValue(theme);
    render(<ThemeToggle />);
    fireEvent.click(screen.getByRole("button"));
    expect(mockToggleTheme).toHaveBeenCalledTimes(1);
  });
});
