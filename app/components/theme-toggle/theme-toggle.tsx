import { css, useTheme } from "@emotion/react";
import { RDSButton } from "@roshn/ui-kit";

interface ExtendedTheme {
  rds: {
    direction: "ltr" | "rtl";
    dimension: Record<string, string>;
    color: {
      lightBlue: Record<string, string>;
      castletonGreen: Record<string, string>;
    };
    borderRadius: Record<string, string>;
  };
  mode: "light" | "dark";
  toggleTheme?: () => void;
}

export function ThemeToggle() {
  const theme = useTheme() as ExtendedTheme;
  const isDark = theme.mode === "dark";
  const styles = theme.rds;

  // For now, we'll just show the current mode without toggle functionality
  // since we don't have a way to update the theme from components yet

  return (
    <RDSButton
      variant="tertiary"
      size="lg"
      text="Toggle Theme"
      leadIcon={
        isDark ? (
          // Sun icon for light mode
          <svg
            css={css`
              width: ${styles.dimension[400]};
              height: ${styles.dimension[400]};
              color: ${styles.color.castletonGreen[900]};
            `}
            fill="none"
            stroke={styles.color.castletonGreen[900]}
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>
        ) : (
          // Moon icon for dark mode
          <svg
            css={css`
              width: ${styles.dimension[400]};
              height: ${styles.dimension[400]};
              color: ${styles.color.castletonGreen[900]};
            `}
            fill="none"
            stroke={styles.color.castletonGreen[900]}
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
            />
          </svg>
        )
      }
      onClick={theme.toggleTheme}
      aria-label={`Switch to ${isDark ? "light" : "dark"} mode`}
    />
  );
}
