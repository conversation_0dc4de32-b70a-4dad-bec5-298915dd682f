import { css } from "@emotion/react";

import { DirectionToggle } from "./DirectionToggle";

export function DirectionExample() {
  const direction = "ltr" as "ltr" | "rtl"; // Simplified for now
  const isRTL = direction === "rtl";

  const containerStyle = css`
    padding: 1rem;
    max-width: 42rem;
    margin: 0 auto;

    & > * + * {
      margin-top: 0.5rem;
    }
  `;

  const headerStyle = css`
    display: flex;
    align-items: center;
    justify-content: space-between;
  `;

  const titleStyle = css`
    font-size: 1.5rem;
    font-weight: 600;
    color: #0066ff;
  `;

  return (
    <div css={containerStyle}>
      <div css={headerStyle}>
        <h2 css={titleStyle}>Direction Support Example</h2>
        <DirectionToggle />
      </div>

      {/* Status display */}
      <div
        css={css`
          background-color: #f0f8ff;
          padding: 1rem;
          border-radius: 8px;
        `}
      >
        <p
          css={css`
            font-size: 0.875rem;
            color: #333;
            margin-bottom: 0.5rem;
          `}
        >
          Current direction: <strong>{direction.toUpperCase()}</strong>
        </p>
        <p
          css={css`
            font-size: 0.875rem;
            color: #28a745;
          `}
        >
          Is RTL: <strong>{isRTL ? "Yes" : "No"}</strong>
        </p>
      </div>

      {/* Direction-aware margin example */}
      <div>
        <h3
          css={css`
            font-size: 1.25rem;
            margin-bottom: 1rem;
            color: #28a745;
          `}
        >
          Direction-Aware Margins
        </h3>
        <div
          css={css`
            padding: 1rem;
            background-color: #f0f8ff;
            border-radius: 8px;
          `}
        >
          <p>This content has direction-aware margins and borders.</p>
          <p
            css={css`
              font-size: 1.125rem;
              color: #28a745;
              margin-top: 1rem;
            `}
          >
            LTR: Left margin + left border | RTL: Right margin + right border
          </p>
        </div>
      </div>

      {/* Text alignment example */}
      <div>
        <h3
          css={css`
            font-size: 1.25rem;
            margin-bottom: 1rem;
            color: #28a745;
          `}
        >
          Text Alignment
        </h3>
        <div
          css={css`
            padding: 1rem;
            background-color: #f0f8ff;
            border-radius: 8px;
          `}
        >
          <p>This text aligns based on the current direction.</p>
          <p
            css={css`
              font-size: 1.125rem;
              color: #28a745;
              margin-top: 1rem;
            `}
          >
            LTR: Left aligned | RTL: Right aligned
          </p>
        </div>
      </div>
    </div>
  );
}
