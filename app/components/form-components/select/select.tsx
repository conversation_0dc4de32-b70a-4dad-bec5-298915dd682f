import { RDSSelect } from "@roshn/ui-kit";
import { Controller, Control, FieldValues, Path } from "react-hook-form";
import { Props } from "react-select";

type RHFSelectProps<T extends FieldValues> = {
  control: Control<T>;
  name: Path<T>;
  label?: string;
  helperText?: string;
  isError?: string | boolean;
  isSuccess?: string | boolean;
  infoIcon?: boolean;
  isRequired?: boolean;
  rtl?: boolean;
  trailIcon?: React.ReactNode;
  options: {
    label: string;
    value: string;
  }[];
} & Omit<Props, "value" | "onChange">;

export const Select = <T extends FieldValues>({ control, name, ...rest }: RHFSelectProps<T>) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <RDSSelect
          {...rest}
          value={value?.value}
          onChange={(e) => onChange((e as { label: string; value: string })?.value)}
          isError={!!error}
          helperText={error?.message || rest?.helperText}
        />
      )}
    />
  );
};
