import { RDSTextArea, RDSTextAreaProps } from "@roshn/ui-kit";
import { Controller, Control, FieldValues, Path } from "react-hook-form";

type ControlledTextInputProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label: string;
  placeholder?: string;
  type?: string;
} & Omit<RDSTextAreaProps, "name" | "value" | "onChange" | "onBlur">;

export function TextArea<T extends FieldValues>({
  name,
  control,
  label,
  placeholder,
  helperText,
  ...rest
}: ControlledTextInputProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <RDSTextArea
          {...field}
          label={label}
          placeholder={placeholder}
          helperText={fieldState.error?.message ?? helperText}
          isInvalid={!!fieldState.error}
          {...rest}
        />
      )}
    />
  );
}
