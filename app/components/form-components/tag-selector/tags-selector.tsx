import { css } from "@emotion/react";
import { AppTheme, RDS<PERSON><PERSON>on, RDSTagInteractive } from "@roshn/ui-kit";
import { useMemo } from "react";
import { Controller, Control, useWatch } from "react-hook-form";

type TagOption = {
  value: string;
  label: string;
  leadIcon?: any;
  disabled?: boolean;
};

type ControlledTagSelectorProps = {
  name: string;
  control: Control;
  options: TagOption[];
  label: string;
  description: string;
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  tagWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["150"],
      flexWrap: "wrap",
    }),

  headingWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),

  label: (theme: AppTheme) =>
    css({
      ...theme.rds.font.fontSize["87"],
      color: theme.rds.color.text.ui.primary,
    }),

  description: (theme: AppTheme) =>
    css({
      ...theme.rds.brand.font.fontFamily.label,
      color: theme.rds.color.text.ui.tertiary,
    }),

  labelWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),
};

export function TagSelector({
  name,
  control,
  options,
  description,
  label,
}: ControlledTagSelectorProps) {
  const selectedValues: string[] = useWatch({ control, name }) || [];

  const activeOptions = useMemo(
    () => options.filter((opt) => !opt.disabled).map((opt) => opt.value),
    [options],
  );

  const allSelected = useMemo(
    () => activeOptions.every((val: string) => selectedValues.includes(val)),
    [activeOptions, selectedValues],
  );

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange } }) => {
        const toggle = (value: string) => {
          if (selectedValues.includes(value)) {
            onChange(selectedValues.filter((v) => v !== value));
          } else {
            onChange([...selectedValues, value]);
          }
        };

        const handleSelectAllToggle = () => {
          if (allSelected) {
            onChange([]); // Deselect all
          } else {
            onChange(activeOptions); // Select all (only non-disabled)
          }
        };

        return (
          <div css={styles.wrapper}>
            <div css={styles.headingWrapper}>
              <div css={styles.labelWrapper}>
                <label css={styles.label}>{label}</label>
                <RDSButton
                  variant="tertiary"
                  size="sm"
                  text={allSelected ? "Deselect All" : "Select All"}
                  onClick={handleSelectAllToggle}
                />
              </div>
              <div css={styles.description}>{description}</div>
            </div>
            <div css={styles.tagWrapper}>
              {options.map(({ value, label, leadIcon, disabled }) => (
                <RDSTagInteractive
                  key={value}
                  label={label}
                  state={(selectedValues.includes(value) ? "active" : "enabled") as any}
                  leadIcon={leadIcon}
                  disabled={disabled}
                  onClick={() => !disabled && toggle(value)}
                />
              ))}
            </div>
          </div>
        );
      }}
    />
  );
}
