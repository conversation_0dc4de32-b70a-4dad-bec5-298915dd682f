import { RDSStepper, RDSStepperProps } from "@roshn/ui-kit";
import { Controller, Control, FieldValues, Path } from "react-hook-form";
import { useEffect, useRef } from "react";

type ControlledStepperProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  type?: string;
} & Omit<RDSStepperProps, "name" | "value" | "onChange" | "onBlur">;

export function InputNumber<T extends FieldValues>({
  name,
  control,
  label,
  helperText,
  ...rest
}: ControlledStepperProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      const buttons = containerRef.current.querySelectorAll('button');
      buttons.forEach(button => {
        if (!button.getAttribute('type')) {
          button.setAttribute('type', 'button');
        }
      });
    }
  });

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <div ref={containerRef}>
          <RDSStepper
            {...field}
            type={"button"}
            value={field.value}
            helperText={fieldState.error?.message ?? helperText}
            isInvalid={!!fieldState.error}
            {...rest}
          />
        </div>
      )}
    />
  );
}
