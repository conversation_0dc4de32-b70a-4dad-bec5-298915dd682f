import { render, RenderResult, screen } from "@testing-library/react";
import { ReactElement, ReactNode } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { describe, expect, it } from "vitest";

import { Input } from "./input";

const renderWithReactHookForm = (
  ui: ReactElement,
  { defaultValues = {} }: { defaultValues?: Record<string, any> } = {},
): RenderResult => {
  const Wrapper = ({ children }: { children: ReactNode }) => {
    const methods = useForm({ defaultValues });

    return <FormProvider {...methods}>{children}</FormProvider>;
  };

  return render(ui, { wrapper: Wrapper });
};

describe("TextInput", () => {
  it("renders the input", () => {
    renderWithReactHookForm(<Input name="name" label="Name" type="text" required />, {
      defaultValues: { name: "" },
    });

    expect(screen.getByText(/Name/i)).toBeInTheDocument();
  });

  it("displays required asterisk when required", () => {
    renderWithReactHookForm(
      <Input id="email" name="email" label="Email" type="text" isRequired />,
      {
        defaultValues: { email: "" },
      },
    );

    expect(screen.getByText("*")).toBeInTheDocument();
    expect(screen.getByText(/Email/i)).toBeInTheDocument();
  });
});
