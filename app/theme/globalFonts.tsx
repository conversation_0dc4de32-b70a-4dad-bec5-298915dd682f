import { Global } from "@emotion/react";
import React from "react";

import {
  IBMPlexSansArabic_Thin_ttf,
  IBMPlexSansArabic_Regular_ttf,
  IBMPlexSansArabic_Light_ttf,
  IBMPlexSansArabic_Bold_ttf,
  IBMPlexSansArabic_ExtraBold_ttf,
} from "~/public/fonts";

const fontFaces = [
  <Global
    key="thin"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "IBM Plex Sans Arabic",
        fontStyle: "normal",
        fontWeight: 100,
        src: `url("${IBMPlexSansArabic_Thin_ttf}") format("truetype")`,
      },
    }}
  />,
  <Global
    key="light"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "IBM Plex Sans Arabic",
        fontStyle: "normal",
        fontWeight: 300,
        src: `url("${IBMPlexSansArabic_Light_ttf}") format("truetype")`,
      },
    }}
  />,
  <Global
    key="regular"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "IBM Plex Sans Arabic",
        fontStyle: "normal",
        fontWeight: 400,
        src: `url("${IBMPlexSansArabic_Regular_ttf}") format("truetype")`,
      },
    }}
  />,

  <Global
    key="bold"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "IBM Plex Sans Arabic",
        fontStyle: "normal",
        fontWeight: 700,
        src: `url("${IBMPlexSansArabic_Bold_ttf}") format("truetype")`,
      },
    }}
  />,

  <Global
    key="extra-bold"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "IBM Plex Sans Arabic",
        fontStyle: "normal",
        fontWeight: 800,
        src: `url("${IBMPlexSansArabic_ExtraBold_ttf}") format("truetype")`,
      },
    }}
  />,
];

export const FontFaces = () => {
  return <>{fontFaces.map((fontFace) => fontFace)}</>;
};
