/**
 * By default, <PERSON> will handle generating the HTTP Response for you.
 * You are free to delete this file if you'd like to, but if you ever want it revealed again, you can run `npx remix reveal` ✨
 * For more information, see https://remix.run/file-conventions/entry.server
 */
import "reflect-metadata/lite";
import { PassThrough } from "node:stream";

import { createNonce, createSecureHeaders, mergeHeaders } from "@mcansh/http-helmet";
import type { EntryContext } from "@remix-run/node";
import { createReadableStreamFromReadable } from "@remix-run/node";
import { RemixServer } from "@remix-run/react";
import { renderToPipeableStream } from "react-dom/server";
import { I18nextProvider } from "react-i18next";

import { NonceProvider } from "./context/csp-provider";
import { bindImplsServer } from "./di/bind-impls.server";
import { createContainer } from "./di/container";
import { DIProvider } from "./hooks/use-di";
import { setupI18nServer } from "./i18n/i18n.server";
import { getLangFromUrl } from "./utils/get-lang";
import { getSecurityConfig, createSecurityHeaders } from "./utils/security-headers.server";

const ABORT_DELAY = 5_000;

export default async function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
) {
  const lang = getLangFromUrl(request.url);
  const serverContainer = createContainer();
  bindImplsServer(serverContainer, lang);
  const i18nInstance = await setupI18nServer(lang, serverContainer);
  const nonce = createNonce();
  const securityConfig = getSecurityConfig();
  const securityHeaders = createSecurityHeaders(nonce, securityConfig);
  const secureHeaders = createSecureHeaders(securityHeaders);

  return new Promise((resolve, reject) => {
    let shellRendered = false;
    const { pipe, abort } = renderToPipeableStream(
      <NonceProvider nonce={nonce}>
        <I18nextProvider i18n={i18nInstance}>
          <DIProvider container={serverContainer}>
            <RemixServer
              context={remixContext}
              url={request.url}
              abortDelay={ABORT_DELAY}
              nonce={nonce}
            />
          </DIProvider>
        </I18nextProvider>
      </NonceProvider>,
      {
        onShellReady() {
          shellRendered = true;
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);

          responseHeaders.set("Content-Type", "text/html");

          resolve(
            new Response(stream, {
              headers: mergeHeaders(responseHeaders, secureHeaders),
              status: responseStatusCode,
            }),
          );

          pipe(body);
        },
        onShellError(error: unknown) {
          reject(error);
        },
        nonce,
        onError(error: unknown) {
          responseStatusCode = 500;
          // Log streaming rendering errors from inside the shell.  Don't log
          // errors encountered during initial shell rendering since they'll
          // reject and get logged in handleDocumentRequest.
          if (shellRendered) {
            console.error(error);
          }
        },
      },
    );

    setTimeout(abort, ABORT_DELAY);
  });
}
