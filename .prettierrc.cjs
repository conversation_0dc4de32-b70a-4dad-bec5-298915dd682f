module.exports = {
  // String quotes - using double quotes as requested
  singleQuote: false,
  jsxSingleQuote: false,
  
  // Semicolons and commas - enabled as requested
  semi: true,
  trailingComma: "all",
  
  // Indentation
  tabWidth: 2,
  useTabs: false,
  
  // Line wrapping
  printWidth: 100,
  
  // JSX and React specific
  bracketSpacing: true,
  bracketSameLine: false,
  
  // Arrow functions
  arrowParens: "always",
  
  // Line endings
  endOfLine: "lf",
  
  // HTML whitespace
  htmlWhitespaceSensitivity: "css",
  
  // Prose wrap for markdown
  proseWrap: "preserve",
  
  // Quote props
  quoteProps: "as-needed",
  
  // File-specific overrides
  overrides: [
    {
      files: "*.json",
      options: {
        printWidth: 200,
        trailingComma: "none"
      }
    },
    {
      files: "*.md",
      options: {
        printWidth: 120,
        proseWrap: "always",
        tabWidth: 2
      }
    },
    {
      files: "*.{yaml,yml}",
      options: {
        tabWidth: 2,
        singleQuote: true
      }
    },
    {
      files: "*.css",
      options: {
        singleQuote: true
      }
    }
  ]
}; 